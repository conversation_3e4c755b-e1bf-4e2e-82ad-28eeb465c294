import { Component, OnInit,Input } from '@angular/core';
import { SalesByHourQueryDto } from 'src/app/pos-server.generated';
import { Observable, from, Subject, combineLatest } from 'rxjs';
import { map } from 'rxjs/operators';
import { Store } from '@ngrx/store';
import * as historySelectors from '../../reducers/customer-club/history/history.selector';
import * as historyActions from '../../reducers/customer-club/history/history.actions';
import * as dailyActions from '../../reducers/daily/daily.actions'
import * as dailySelectors from '../../reducers/daily/daily.selectors'
import { AppState } from 'src/app/reducers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { toFinancialString } from 'src/app/utility/math-helpers';

@Component({
  selector: 'pos-sales-by-hour-table',
  templateUrl: './sales-by-hour-table.component.html',
  styleUrls: ['./sales-by-hour-table.component.scss']
})

export class SalesByHourTableComponent implements OnInit {
  public salesByHour$: Observable<SalesByHourQueryDto[]>;
  public refundsByHour$: Observable<SalesByHourQueryDto[]>;
  public netSalesByHour$: Observable<SalesByHourQueryDto[]>;
  public currentView: 'net' | 'sales' | 'refunds' = 'net';

  constructor(private store: Store<AppState>){}

  ngOnInit(): void {
    this.salesByHour$ = this.store.select(dailySelectors.storeSalesByHour);
    this.refundsByHour$ = this.store.select(dailySelectors.storeRefundsByHour);
    this.store.dispatch(dailyActions.getSalesByHour({}));
    this.store.dispatch(dailyActions.getRefundsByHour({}));

    // Calculate net sales by combining sales and refunds data
    this.netSalesByHour$ = combineLatest([this.salesByHour$, this.refundsByHour$]).pipe(
      map(([sales, refunds]) => {
        const netSales: SalesByHourQueryDto[] = [];

        // Create maps for easy lookup
        const salesMap = new Map<string, SalesByHourQueryDto>();
        const refundsMap = new Map<string, SalesByHourQueryDto>();

        sales.forEach(sale => salesMap.set(sale.hours, sale));
        refunds.forEach(refund => refundsMap.set(refund.hours, refund));

        // Get all unique hours from both sales and refunds
        const allHours = new Set([...sales.map(s => s.hours), ...refunds.map(r => r.hours)]);

        // Calculate net sales for each hour
        allHours.forEach(hour => {
          const sale = salesMap.get(hour);
          const refund = refundsMap.get(hour);

          const netSale: SalesByHourQueryDto = {
            hours: hour,
            sales: (sale ? sale.sales : 0) + (refund ? refund.sales : 0),
            items: (sale ? sale.items : 0) + (refund ? refund.items : 0),
            custs: (sale ? sale.custs : 0) + (refund ? refund.custs : 0)
          };
          netSales.push(netSale);
        });

        // Sort by hour for consistent display
        return netSales.sort((a, b) => a.hours.localeCompare(b.hours));
      })
    );
  }

  toggleView(): void {
    if (this.currentView === 'net') {
      this.currentView = 'sales';
    } else if (this.currentView === 'sales') {
      this.currentView = 'refunds';
    } else {
      this.currentView = 'net';
    }
  }
  
  toFinancialString(val) {
    if (val < 0) {
      return `-${toFinancialString(-1*val)}`
    }else {
      return toFinancialString(val)
    }
  }
}
