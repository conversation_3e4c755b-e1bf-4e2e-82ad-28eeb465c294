import { Component, OnInit,Input } from '@angular/core';
import { DepartmentSalesDto } from 'src/app/pos-server.generated';
import { Observable, from, Subject, combineLatest } from 'rxjs';
import { map } from 'rxjs/operators';
import { Store } from '@ngrx/store';
import * as dailyActions from '../../reducers/daily/daily.actions'
import * as dailySelectors from '../../reducers/daily/daily.selectors'
import { AppState } from 'src/app/reducers';
import { toFinancialString } from 'src/app/utility/math-helpers';

@Component({
  selector: 'pos-department-sales',
  templateUrl: './department-sales.component.html',
  styleUrls: ['./department-sales.component.scss']
})
export class DepartmentSalesComponent implements OnInit {

  constructor(private store: Store<AppState>){}
  public departmentSales$: Observable<DepartmentSalesDto[]>;
  public departmentRefunds$: Observable<DepartmentSalesDto[]>;
  public netDepartmentSales$: Observable<DepartmentSalesDto[]>;
  public currentView: 'net' | 'sales' | 'refunds' = 'net';

  ngOnInit(): void {
    this.departmentSales$ = this.store.select(dailySelectors.getDepartmentSales)
    this.departmentRefunds$ = this.store.select(dailySelectors.storeDepartmentRefunds)
    this.store.dispatch(dailyActions.getDepartmentSales({}))
    this.store.dispatch(dailyActions.getDepartmentRefunds({}))

    // Calculate net sales by combining sales and refunds data
    this.netDepartmentSales$ = combineLatest([this.departmentSales$, this.departmentRefunds$]).pipe(
      map(([sales, refunds]) => {
        const netSales: DepartmentSalesDto[] = [];

        // Create maps for easy lookup
        const salesMap = new Map<string, DepartmentSalesDto>();
        const refundsMap = new Map<string, DepartmentSalesDto>();

        sales.forEach(sale => salesMap.set(sale.departmentName, sale));
        refunds.forEach(refund => refundsMap.set(refund.departmentName, refund));

        // Get all unique department names from both sales and refunds
        const allDepartments = new Set([...sales.map(s => s.departmentName), ...refunds.map(r => r.departmentName)]);

        // Calculate net sales for each department
        allDepartments.forEach(departmentName => {
          const sale = salesMap.get(departmentName);
          const refund = refundsMap.get(departmentName);

          const netSale: DepartmentSalesDto = {
            departmentName: departmentName,
            amount: (sale ? sale.amount : 0) + (refund ? refund.amount : 0)
          };
          netSales.push(netSale);
        });

        // Sort by department name for consistent display
        return netSales.sort((a, b) => a.departmentName.localeCompare(b.departmentName));
      })
    );
  }

  toggleView(): void {
    if (this.currentView === 'net') {
      this.currentView = 'sales';
    } else if (this.currentView === 'sales') {
      this.currentView = 'refunds';
    } else {
      this.currentView = 'net';
    }
  }
  
  toFinancialString(val) {
    if (val < 0) {
      return `-${toFinancialString(-1*val)}`
    }else {
      return toFinancialString(val)
    }
  }
}
