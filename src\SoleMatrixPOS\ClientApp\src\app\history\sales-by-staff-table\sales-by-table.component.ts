import { Component, OnInit,Input } from '@angular/core';
import { SalesByStaffQueryDto } from 'src/app/pos-server.generated';
import { Observable, from, Subject, combineLatest } from 'rxjs';
import { map } from 'rxjs/operators';
import { Store } from '@ngrx/store';
import * as historySelectors from '../../reducers/customer-club/history/history.selector';
import * as historyActions from '../../reducers/customer-club/history/history.actions';
import * as dailyActions from '../../reducers/daily/daily.actions'
import * as dailySelectors from '../../reducers/daily/daily.selectors'
import { AppState } from 'src/app/reducers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { toFinancialString } from 'src/app/utility/math-helpers';

@Component({
  selector: 'pos-sales-by-table',
  templateUrl: './sales-by-table.component.html',
  styleUrls: ['./sales-by-table.component.scss']
})

export class SalesByTableComponent implements OnInit {
  public salesByStaff$: Observable<SalesByStaffQueryDto[]>;
  public refundsByStaff$: Observable<SalesByStaffQueryDto[]>;
  public netSalesByStaff$: Observable<SalesByStaffQueryDto[]>;
  public currentView: 'net' | 'sales' | 'refunds' = 'net';

  constructor(private store: Store<AppState>){}

  ngOnInit(): void {
    this.salesByStaff$ = this.store.select(dailySelectors.storeSalesByStaff);
    this.refundsByStaff$ = this.store.select(dailySelectors.storeRefundsByStaff);
    this.store.dispatch(dailyActions.getSalesByStaff({}));
    this.store.dispatch(dailyActions.getRefundsByStaff({}));

    // Calculate net sales by combining sales and refunds data
    this.netSalesByStaff$ = combineLatest([this.salesByStaff$, this.refundsByStaff$]).pipe(
      map(([sales, refunds]) => {
        const netSales: SalesByStaffQueryDto[] = [];

        // Create maps for easy lookup
        const salesMap = new Map<string, SalesByStaffQueryDto>();
        const refundsMap = new Map<string, SalesByStaffQueryDto>();

        sales.forEach(sale => salesMap.set(sale.staff_Name, sale));
        refunds.forEach(refund => refundsMap.set(refund.staff_Name, refund));

        // Get all unique staff names from both sales and refunds
        const allStaff = new Set([...sales.map(s => s.staff_Name), ...refunds.map(r => r.staff_Name)]);

        // Calculate net sales for each staff member
        allStaff.forEach(staffName => {
          const sale = salesMap.get(staffName);
          const refund = refundsMap.get(staffName);

          const netSale: SalesByStaffQueryDto = {
            staff_Name: staffName,
            sales: (sale ? sale.sales : 0) + (refund ? refund.sales : 0),
            items: (sale ? sale.items : 0) + (refund ? refund.items : 0),
            custs: (sale ? sale.custs : 0) + (refund ? refund.custs : 0)
          };
          netSales.push(netSale);
        });

        // Sort by staff name for consistent display
        return netSales.sort((a, b) => a.staff_Name.localeCompare(b.staff_Name));
      })
    );
  }

  toggleView(): void {
    if (this.currentView === 'net') {
      this.currentView = 'sales';
    } else if (this.currentView === 'sales') {
      this.currentView = 'refunds';
    } else {
      this.currentView = 'net';
    }
  }
  
  toFinancialString(val) {
    if (val < 0) {
      return `-${toFinancialString(-1*val)}`
    }else {
      return toFinancialString(val)
    }
  }
}
