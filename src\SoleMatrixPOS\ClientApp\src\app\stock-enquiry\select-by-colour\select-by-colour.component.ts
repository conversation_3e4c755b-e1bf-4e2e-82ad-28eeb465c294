import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { StockEnquiryGetAllColorDto, StockEnquiryRequestDto } from 'src/app/pos-server.generated';
import { AppState } from 'src/app/reducers';
import * as StockEnquireSelector from '../../reducers/stock-enquiry/stock-enquiry.selectors';

@Component({
  selector: 'pos-select-by-colour',
  templateUrl: './select-by-colour.component.html',
  styleUrls: ['./select-by-colour.component.scss']
})
export class SelectByColourComponent implements OnInit {

  colors$: Observable<StockEnquiryGetAllColorDto[]>;
  currentColorIndex: number = 0;
  colors: StockEnquiryGetAllColorDto[] = [];

  @Output() selectedItemChanged = new EventEmitter<StockEnquiryRequestDto>();
  @Input() selectedColor: StockEnquiryGetAllColorDto;

  constructor(private store: Store<AppState>) { }

  ngOnInit() {
    this.colors$ = this.store.select(StockEnquireSelector.stockEnquiryColor);

    // Subscribe to colors to get the array for navigation
    this.colors$.subscribe(colors => {
      this.colors = colors || [];
      // Set initial color if none selected and colors are available
      if (!this.selectedColor && this.colors.length > 0) {
        this.selectColorByIndex(0);
      }
    });
  }

  getCurrentColorName(): string {
    if (this.selectedColor) {
      return this.selectedColor.colorName;
    }
    return this.colors.length > 0 ? this.colors[0].colorName : 'No colors available';
  }

  nextColor() {
    if (this.colors.length > 1) {
      this.currentColorIndex = (this.currentColorIndex + 1) % this.colors.length;
      this.selectColorByIndex(this.currentColorIndex);
    }
  }

  previousColor() {
    if (this.colors.length > 1) {
      this.currentColorIndex = this.currentColorIndex === 0
        ? this.colors.length - 1
        : this.currentColorIndex - 1;
      this.selectColorByIndex(this.currentColorIndex);
    }
  }

  canGoNext(): boolean {
    return this.colors.length > 1;
  }

  canGoPrevious(): boolean {
    return this.colors.length > 1;
  }

  private selectColorByIndex(index: number) {
    if (index >= 0 && index < this.colors.length) {
      const color = this.colors[index];
      this.selectedColor = color;
      this.selectedItemChanged.emit(color);
    }
  }

  selectItem(selectedValue: string) {
    // Parse the selectedValue to extract styleCode, colorCode, and colorName
    const [styleCode, colorCode, colorName] = selectedValue.split('|');

    // Create a new StockEnquiryRequestDto object with extracted properties
    const selectedItem: StockEnquiryGetAllColorDto = {
      styleCode,
      colorCode,
      colorName
    };

    // Emit the selected item object to the parent component
    this.selectedItemChanged.emit(selectedItem);
  }

  getSelectedValue(): string {
    if (!this.selectedColor) return '';
    return `${this.selectedColor.styleCode}|${this.selectedColor.colorCode}|${this.selectedColor.colorName}`;
  }

  trackByColor(index: number, color: StockEnquiryGetAllColorDto): string {
    return color.colorCode; // Use a unique identifier for trackBy
  }
}
