<div class="d-flex justify-content-between align-items-center">
    <h3 *ngIf="currentView === 'net'">Net Sales By Staff</h3>
    <h3 *ngIf="currentView === 'sales'">Sales By Staff</h3>
    <h3 *ngIf="currentView === 'refunds'">Refunds By Staff</h3>
    <button class="btn btn-link" (click)="toggleView()">
        <i class="fas fa-exchange-alt"></i>
    </button>
</div>
<div class="table-responsive">
    <div *ngIf="currentView === 'net'">
        <table class="table table-striped ml-2 mr-2 table-hover">
            <thead>
                <tr>
                    <th scope="col-1">Staff Name</th>
                    <th scope="col-1">Net Sales</th>
                    <th scope="col-2">Items</th>
                    <th scope="col-1">Custs</th>
                    <th scope="col-1"> </th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let staff of netSalesByStaff$|async; let i = index">
                    <td>
                        {{staff.staff_Name}}
                    </td>
                    <td>
                        {{toFinancialString(staff.sales)}}
                    </td>
                    <td>
                        {{staff.items}}
                    </td>
                    <td>
                        {{staff.custs}}
                    </td>
                    <td></td>
                </tr>
            </tbody>
        </table>
    </div>
    <div *ngIf="currentView === 'sales'">
        <table class="table table-striped ml-2 mr-2 table-hover">
            <thead>
                <tr>
                    <th scope="col-1">Staff Name</th>
                    <th scope="col-1">Sales</th>
                    <th scope="col-2">Items</th>
                    <th scope="col-1">Custs</th>
                    <th scope="col-1"> </th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let staff of salesByStaff$|async; let i = index">
                    <td>
                        {{staff.staff_Name}}
                    </td>
                    <td >

                        {{toFinancialString(staff.sales)}}
                    </td>
                    <td >

                        {{staff.items}}
                    </td>
                    <td>
                        {{staff.custs}}
                    </td>
                    <td></td>
                </tr>
            </tbody>
        </table>
    </div>
    <div *ngIf="currentView === 'refunds'">
        <table class="table table-striped ml-2 mr-2 table-hover">
            <thead>
                <tr>
                    <th scope="col-1">Staff Name</th>
                    <th scope="col-1">Refunds</th>
                    <th scope="col-2">Items</th>
                    <th scope="col-1">Custs</th>
                    <th scope="col-1"> </th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let staff of refundsByStaff$|async; let i = index">
                    <td>
                        {{staff.staff_Name}}
                    </td>
                    <td >

                        {{toFinancialString(staff.sales)}}
                    </td>
                    <td >

                        {{staff.items}}
                    </td>
                    <td>
                        {{staff.custs}}
                    </td>
                    <td></td>
                </tr>
            </tbody>
        </table>
    </div>
</div>