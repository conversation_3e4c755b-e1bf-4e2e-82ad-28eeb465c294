using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System.Collections.Generic;
using System;
using System.IO;
using AutoMapper;
using SoleMatrixPOS.Application.Infrastructure;
using System.Linq;
using SoleMatrixPOS.Email;
using SelectPdf; // HTML-to-PDF converter
using System.Text;
using System.Globalization;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using SoleMatrixPOS.Application.ReceiptPrinting;
using BarcodeStandard;
using SoleMatrixPOS.Domain.Models;
using SoleMatrixPOS.Domain.RepositoryInterfaces;
using SoleMatrixPOS.Dal.Interface.Models.SMPos;
using System.Drawing.Imaging;
using System.Drawing;
using SkiaSharp;
using System.Security.Cryptography.X509Certificates;
using SoleMatrixPOS.Application.Transaction;
using SoleMatrixPOS.Application.GiftVoucher;
using static EmailController;
using Org.BouncyCastle.Asn1.Ocsp;
using System.Configuration;
using SoleMatrixPOS.Application.Layby.Queries;

[ApiController]
[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
[Route("api/[controller]")]
public class EmailController : ControllerBase
{
	private readonly IHouseKeepingRepository _houseKeepingRepository;
	private readonly ICreditNoteRepository _crnRepository;
	private readonly IGiftVoucherRepository _giftVoucherRepository;
	private readonly IReceiptPrintingRepository _receiptPrintingRepository;
	private readonly StaffCodeContext _staffCodeContext;
	private readonly ILaybySearchRepository _laybySearchRepository;
	private readonly ILaybyRepository _laybyRepository;
	private readonly IMapper _mapper;
	private readonly IEmailService _emailService;
	private readonly StoreTimeContext _storeTimeContext;

	public EmailController(
		IEmailService emailService,
		IMapper mapper,
		IHouseKeepingRepository houseKeepingRepository,
		ILaybySearchRepository laybySearchRepository,
		IGiftVoucherRepository giftVoucherRepository,
		ILaybyRepository laybyRepository,
		IReceiptPrintingRepository receiptPrintingRepository,
		ICreditNoteRepository crnRepository,
		StaffCodeContext staffCodeContext,
		StoreTimeContext storeTimeContext)
	{
		_receiptPrintingRepository = receiptPrintingRepository;
		_mapper = mapper;
		_houseKeepingRepository = houseKeepingRepository;
		_crnRepository = crnRepository;
		_laybySearchRepository = laybySearchRepository;
		_laybyRepository = laybyRepository;
		_giftVoucherRepository = giftVoucherRepository;
		_staffCodeContext = staffCodeContext;
		_emailService = emailService;
		_storeTimeContext = storeTimeContext;
	}

	[HttpPost("get-receipt-pdf")]
	public async Task<IActionResult> GetReceiptPdf([FromBody] ReceiptTransactionDto receiptTransactionDto)
	{
		if (receiptTransactionDto == null)
		{
			return BadRequest("Receipt data is required.");
		}

		try
		{
			string pinPadNo = _staffCodeContext.PINPadNo;
			string storeId = _staffCodeContext.StoreDetailsDto.StoreId;

			// Retrieve header and footer arrays as before
			var headerArray = await _houseKeepingRepository.GetReceiptHeader(
				_storeTimeContext.StoreLocalTime.Date, storeId, pinPadNo);
			var titleArray = await _houseKeepingRepository.GetReceiptTitle(
	_storeTimeContext.StoreLocalTime.Date, storeId, pinPadNo);
			var footerArray = await _houseKeepingRepository.GetReceiptFooter(
				_storeTimeContext.StoreLocalTime.Date, storeId, pinPadNo);

			const EmailRequest emailRequest = null; // Placeholder for emailRequest, if needed in future


			string staffLine = $"Served by {await _receiptPrintingRepository.StaffName(_staffCodeContext.StaffCode)}"; // not from history staffContext should be fine

			// Check for any layby messages if applicable
			IEnumerable<ReceiptLayby> laybyMessages = null;
			var sysControl = await _houseKeepingRepository.GetSysStatus(storeId);
			if (sysControl != null &&
				sysControl.LAYBY_NUMBER_OF_WEEKS?.ToUpper() == "T" &&
				(receiptTransactionDto.TransType == 4 || receiptTransactionDto.TransType == 5))
			{
				laybyMessages = await _houseKeepingRepository.GetReceiptLayby(storeId);
			}

			var giftVoucherDetails = new List<Giftvoucher>();
			var creditNoteDetails = new List<Creditnote>();
			if (receiptTransactionDto.pays != null)
			{
				foreach (var transPay in receiptTransactionDto.pays)
				{
					if (transPay.PaymentType == "Credit Note")
					{

						var results = await _crnRepository.SearchUnredeemed(transPay.VoucherNumber);

						if (results == null)
						{
							results = await _crnRepository
								.SearchRedeemed(transPay.VoucherNumber);
						}

						if (results != null)
						{
							creditNoteDetails.Add(results);
						}
					}
					else
					{
						var results = await _giftVoucherRepository
							.SearchUnredeemed(transPay.VoucherNumber);

						// if none, try redeemed
						if (results == null)
						{
							results = await _giftVoucherRepository
								.SearchRedeemed(transPay.VoucherNumber);
						}

						// if we got anything, add them
						if (results != null)
						{
							giftVoucherDetails.Add(results);
						}
					}
				}
			}

			// Generate the PDF using your existing method
			byte[] pdfBytes = GenerateReceiptPdf(receiptTransactionDto, titleArray, headerArray, footerArray, staffLine, emailRequest, giftVoucherDetails, creditNoteDetails, laybyMessages);

			// Return the PDF as a FileContentResult
			return File(pdfBytes, "application/pdf", "receipt.pdf");
		}
		catch (Exception ex)
		{
			return StatusCode(500, $"Internal server error: {ex.Message}");
		}
	}

	[HttpPost("send-gift-voucher")]
	public async Task<IActionResult> SendGiftVoucher([FromBody] GiftVoucherRequest request)
	{
		if (string.IsNullOrWhiteSpace(request.Email))
		{
			return BadRequest("Email is required.");
		}

		if (string.IsNullOrWhiteSpace(request.VoucherNumber) || request.VoucherNumber.Length != 13)
		{
			return BadRequest("Valid 13-digit voucher number is required.");
		}

		if (request.VoucherValue <= 0)
		{
			return BadRequest("Voucher value must be greater than zero.");
		}

		try
		{
			string storeId = _staffCodeContext.StoreDetailsDto.StoreId;

			// Get store details for organization name
			var titleArray = await _houseKeepingRepository.GetReceiptTitle(
				_storeTimeContext.StoreLocalTime.Date, storeId, _staffCodeContext.PINPadNo);
			string organizationName = titleArray?.FirstOrDefault()?.RecLine ?? "SoleMatrix";

			// Generate the PDF
			byte[] pdfAttachment = GenerateGiftVoucherPdf(
				organizationName,
				request.VoucherNumber,
				request.VoucherValue,
				request.VoucherType);

			// Prepare subject & body based on voucher type
			string subject;
			if (request.VoucherType == "Credit Note")
			{
				subject = $"Your {organizationName} Credit Note";
			}
			else if (request.VoucherType == "Gift Card")
			{
				subject = $"Your {organizationName} Gift Card";
			}
			else
			{
				// Fallback for any other voucher types
				subject = $"Your {organizationName} Voucher";
			}

			// Email the voucher
			await _emailService.SendReceiptEmailAsync(
				request.Email,
				_storeTimeContext.StoreLocalTime,
				subject,
				pdfAttachment,
				organizationName);

			return Ok();
		}
		catch (Exception ex)
		{
			return StatusCode(500, $"Internal server error: {ex.Message}");
		}
	}

	[HttpPost("send-receipt-by-transno")]
	public async Task<IActionResult> SendReceiptByTransNo([FromBody] TransNoRequest transNoRequest)
	{
		if (transNoRequest.TransNo == null)
		{
			return BadRequest("Transaction number is required.");
		}

		// Retrieve transaction logs and payment details using TransNo
		ReceiptTransactionDto receiptTransaction = new ReceiptTransactionDto();
		receiptTransaction.logs = await _receiptPrintingRepository.GetTransactionLogs(
			transNoRequest.TransNo.Value,
			_staffCodeContext.StoreDetailsDto.StoreId,
			_staffCodeContext.PINPadNo);
		receiptTransaction.pays = await _receiptPrintingRepository.GetTransactionPay(
			transNoRequest.TransNo.Value,
			_staffCodeContext.StoreDetailsDto.StoreId,
			_staffCodeContext.PINPadNo);
		receiptTransaction.refs = await _receiptPrintingRepository.GetTransactionRef(
			transNoRequest.TransNo.Value,
			_staffCodeContext.StoreDetailsDto.StoreId,
			_staffCodeContext.PINPadNo);

		if (receiptTransaction.logs == null || !receiptTransaction.logs.Any())
		{
			return NotFound("No transaction found for the provided TransNo.");
		}


		// Why are we using the first log rather than the last log?

		// Set the sale date and time based on the transaction logs
		var firstLog = receiptTransaction.logs.FirstOrDefault();
		if (firstLog != null && firstLog.TransactionDate.HasValue && !string.IsNullOrEmpty(firstLog.TransactionTime))
		{
			try
			{
				DateTime timePart;
				// Try parsing with both single and double digit hour formats
				if (!DateTime.TryParseExact(
					firstLog.TransactionTime.Trim(),
					"h:mm:ss tt",
					CultureInfo.InvariantCulture,
					DateTimeStyles.None,
					out timePart) &&
				!DateTime.TryParseExact(
					firstLog.TransactionTime.Trim(),
					"hh:mm:ss tt",
					CultureInfo.InvariantCulture,
					DateTimeStyles.None,
					out timePart))
				{
					throw new FormatException("Could not parse time in either format");
				}
				receiptTransaction.SaleDateTime = firstLog.TransactionDate.Value.Date.Add(timePart.TimeOfDay);
				receiptTransaction.TransType = firstLog.TransType;
			}
			catch (FormatException)
			{
				receiptTransaction.SaleDateTime = firstLog.TransactionDate.Value.Date;
			}
		}
		else
		{
			receiptTransaction.SaleDateTime = firstLog?.TransactionDate?.Date ?? DateTime.MinValue;
		}

		try
		{
			string pinPadNo = _staffCodeContext.PINPadNo;
			string storeId = _staffCodeContext.StoreDetailsDto.StoreId;
			string staffLine = "";
			try
			{
				staffLine = $"Served by {await _receiptPrintingRepository.StaffName(receiptTransaction.logs.FirstOrDefault().StaffCode)}";
			}
			catch (Exception) { } // shoud just send regardles of error. also todo add serilog logging



			// Retrieve the normal header and footer arrays
			var headerArray = await _houseKeepingRepository.GetReceiptHeader(
				receiptTransaction.SaleDateTime, storeId, pinPadNo);
			var footerArray = await _houseKeepingRepository.GetReceiptFooter(
				receiptTransaction.SaleDateTime, storeId, pinPadNo);
			var titleArray = await _houseKeepingRepository.GetReceiptTitle(
				_storeTimeContext.StoreLocalTime.Date, storeId, pinPadNo);
			string title = titleArray?.FirstOrDefault()?.RecLine ?? "";

			const EmailRequest emailRequest = null; // Placeholder for emailRequest, if needed in future

			// *** New: Check SYSCONTROL configuration ***
			// Instead of replacing headerArray, we now get the layby messages to append later.
			IEnumerable<ReceiptLayby> laybyMessages = null;
			var sysControl = await _houseKeepingRepository.GetSysStatus(storeId);
			if (sysControl != null &&
				sysControl.LAYBY_NUMBER_OF_WEEKS?.ToUpper() == "T" &&
				(receiptTransaction.TransType == 4 || receiptTransaction.TransType == 5))
			{
				laybyMessages = await _houseKeepingRepository.GetReceiptLayby(storeId);
			}

			var giftVoucherDetails = new List<Giftvoucher>();
			var creditNoteDetails = new List<Creditnote>();
			if (receiptTransaction.pays != null)
			{
				foreach (var transPay in receiptTransaction.pays)
				{
					if (transPay.PaymentType == "Credit Note")
					{

						var results = await _crnRepository.SearchUnredeemed(transPay.VoucherNumber);

						if (results == null)
						{
							results = await _crnRepository
								.SearchRedeemed(transPay.VoucherNumber);
						}

						if (results != null)
						{
							creditNoteDetails.Add(results);
						}
					}
					else
					{
						var results = await _giftVoucherRepository
							.SearchUnredeemed(transPay.VoucherNumber);

						// if none, try redeemed
						if (results == null)
						{
							results = await _giftVoucherRepository
								.SearchRedeemed(transPay.VoucherNumber);
						}

						// if we got anything, add them
						if (results != null)
						{
							giftVoucherDetails.Add(results);
						}
					}
				}
			}

			// Get email template based on transaction type and whether it's a layby edit
			string subject;
			bool isLaybyEdit = false;

			// For layby transactions (transType 4), check if it's actually an edit by examining layby lines
			if (receiptTransaction.TransType == 4 && transNoRequest.TransNo.HasValue)
			{
				try
				{
					string laybyCode = await _laybyRepository.GetLaybyCodeByTransNo(transNoRequest.TransNo.Value);
					if (!string.IsNullOrEmpty(laybyCode))
					{
						IEnumerable<Laybyline> laybyLines = await _laybySearchRepository.GetLaybyLines(laybyCode);
						isLaybyEdit = IsLaybyEditFromLaybyLines(laybyLines);
					}
				}
				catch (Exception)
				{
					// If we can't fetch layby lines, assume it's not an edit
					isLaybyEdit = false;
				}
			}

			if (receiptTransaction.TransType == 4 && isLaybyEdit)
			{
				subject = "Your Reprinted Layby Edit Receipt from SoleMatrixPOS";
			}
			else
			{
				var (templateSubject, htmlMessage) = GetEmailTemplate(receiptTransaction.TransType);
				subject = $"Your Reprinted {templateSubject}"; // Add "Reprinted" to the subject
			}

			if (receiptTransaction.TransType == 5)
			{
				if (transNoRequest.TransNo.HasValue)
				{
					// 1) fetch your layby lines
					string laybyCode = await _laybyRepository.GetLaybyCodeByTransNo(transNoRequest.TransNo.Value);
					IEnumerable<Laybyline> laybyLines = await _laybySearchRepository.GetLaybyLines(laybyCode);

					// 2) map Laybyline → TranslogDto and separate by type
					var allLaybyLines = (laybyLines ?? Enumerable.Empty<Laybyline>())
						// drop exactly the last laybyLine
						.SkipLast(1)
						.ToArray();

					// 3) Separate items from prior payments using the original Laybyline objects
					var itemLines = allLaybyLines.Where(ll =>
						ll.TransType == 1 || ll.TransType == 5 || ll.TransType == 7).ToArray();
					var priorPaymentLines = allLaybyLines.Where(ll =>
						ll.TransType == 2 || ll.TransType == 3).ToArray();

					// 4) Convert item lines to TranslogDto for aggregation
					var itemLogDtos = itemLines.Select(ll => new TranslogDto
					{
						StyleCode = ll.StyleCode,
						ColourCode = ll.ColourCode,
						SizeCode = ll.SizeCode,
						Quantity = ll.Quantity.Value,
						SellingPrice = ll.SellingPrice.Value,
						LineNo = (int)ll.LineNo,
						TransactionDate = receiptTransaction.SaleDateTime,
						TransactionTime = receiptTransaction.SaleDateTime.ToString("HH:mm:ss")
					}).ToArray();

					// 3) map existing domain-logs → TranslogDto[] first
					TranslogDto[] existingLogDtos = (receiptTransaction.logs ?? Enumerable.Empty<Translog>())
						.Select(log => new TranslogDto
						{
							StyleCode = log.StyleCode,
							ColourCode = log.ColourCode,
							SizeCode = log.SizeCode,
							Quantity = log.Quantity.Value,
							SellingPrice = log.SellingPrice.Value,
							LineNo = (int)log.LineNo,
							ClientCode = log.ClientCode,
							TransNo = log.TransNo,
							DiscReasonCode = log.DiscReasonCode,
							NettSelling = log.NettSelling,
							TransactionDate = log.TransactionDate,
							TransactionTime = log.TransactionTime
						})
						.ToArray();

					// 4) combine layby items and transaction logs first
					TranslogDto[] rawCombinedLogs = itemLogDtos.Concat(existingLogDtos).ToArray();

					// 5) Aggregate ALL items (layby + transaction) to handle removed items properly
					var aggregatedItems = AggregateLaybyItemsForReprint(rawCombinedLogs);

					// 6) Convert prior payments to have negative selling prices
					var formattedPriorPayments = priorPaymentLines.Select(ll => new TranslogDto
					{
						StyleCode = ll.StyleCode,
						ColourCode = ll.ColourCode,
						SizeCode = ll.SizeCode,
						Quantity = ll.Quantity.Value,
						SellingPrice = -Math.Abs(ll.SellingPrice.Value), // Make negative for prior payments
						LineNo = (int)ll.LineNo,
						TransactionDate = receiptTransaction.SaleDateTime,
						TransactionTime = receiptTransaction.SaleDateTime.ToString("HH:mm:ss")
					}).ToArray();

					// 7) Final combined logs with aggregated items and prior payments
					TranslogDto[] combinedLogs = aggregatedItems.Concat(formattedPriorPayments).ToArray();

					// 5) map your payments into TranspayDto[]
					TranspayDto[] combinedPays = (receiptTransaction.pays ?? Enumerable.Empty<Transpay>())
						.Select(p => new TranspayDto
						{
							PaymentType = p.PaymentType,
							PayAmount = p.PayAmount.Value,
							VoucherNumber = p.VoucherNumber,
							TransNo = p.TransNo
						})
						.ToArray();

					// Use aggregated items to get correct total cost
					var aggregatedItemsForTotal = AggregateLaybyItems(combinedLogs);
					float totalCost = aggregatedItemsForTotal.Sum(l => l.NettSelling.HasValue
						? l.NettSelling.Value
						: l.Quantity * l.SellingPrice);

					float totalPaid = combinedPays.Sum(p => p.PayAmount);
					float remainingBalance = totalCost - totalPaid;

					// 6) Use the layby edit detection that was done earlier
					byte[] pdfAttachment;
					if (isLaybyEdit)
					{
						// Use layby edit receipt format
						pdfAttachment = GenerateLaybyEditPdf(
							receiptTransaction,
							titleArray,
							headerArray,
							footerArray,
							laybyMessages,
							combinedLogs,
							combinedPays,
							transNoRequest.TransNo.Value,
							amountDue: remainingBalance,
							change: 0,
							staffLine,
							isReprint: true
						);
					}
					else
					{
						// Use regular layby receipt format
						pdfAttachment = GenerateLaybyReceiptPdf(
							receiptTransaction,
							titleArray,
							headerArray,
							footerArray,
							laybyMessages,
							combinedLogs,
							combinedPays,
							transNoRequest.TransNo.Value,
							amountDue: remainingBalance,
							change: 0,
							staffLine,
							isReprint: true
						);
					}


					await _emailService.SendReceiptEmailAsync(
						transNoRequest.Email,
						_storeTimeContext.StoreLocalTime,
						subject,
						pdfAttachment,
						title);

					return Ok();
				}
				else
				{
					// Handle the case where TransNo is null (e.g., throw an exception or return an error response)
					throw new ArgumentNullException(nameof(transNoRequest.TransNo), "Transaction number cannot be null.");
				}
			}
			else if (receiptTransaction.TransType == 4)
			{
				// Handle layby transactions (both new laybys and layby edits)
				if (transNoRequest.TransNo.HasValue)
				{
					// 1) fetch your layby lines to get complete layby history
					string laybyCode = await _laybyRepository.GetLaybyCodeByTransNo(transNoRequest.TransNo.Value);
					if (!string.IsNullOrEmpty(laybyCode))
					{
						IEnumerable<Laybyline> laybyLines = await _laybySearchRepository.GetLaybyLines(laybyCode);

						// 2) map Laybyline → TranslogDto and separate by type
						var allLaybyLines = (laybyLines ?? Enumerable.Empty<Laybyline>())
							// drop exactly the last laybyLine
							.SkipLast(1)
							.ToArray();

						// 3) Separate items from prior payments using the original Laybyline objects
						var itemLines = allLaybyLines.Where(ll =>
							ll.TransType == 1 || ll.TransType == 5 || ll.TransType == 7).ToArray();
						var priorPaymentLines = allLaybyLines.Where(ll =>
							ll.TransType == 2 || ll.TransType == 3).ToArray();

						// 4) Convert item lines to TranslogDto for aggregation
						var itemLogDtos = itemLines.Select(ll => new TranslogDto
						{
							StyleCode = ll.StyleCode,
							ColourCode = ll.ColourCode,
							SizeCode = ll.SizeCode,
							Quantity = ll.Quantity.Value,
							SellingPrice = ll.SellingPrice.Value,
							LineNo = (int)ll.LineNo,
							TransactionDate = receiptTransaction.SaleDateTime,
							TransactionTime = receiptTransaction.SaleDateTime.ToString("HH:mm:ss")
						}).ToArray();

						// 5) map existing domain-logs → TranslogDto[] first
						TranslogDto[] existingLogDtos = (receiptTransaction.logs ?? Enumerable.Empty<Translog>())
							.Select(log => new TranslogDto
							{
								StyleCode = log.StyleCode,
								ColourCode = log.ColourCode,
								SizeCode = log.SizeCode,
								Quantity = log.Quantity.Value,
								SellingPrice = log.SellingPrice.Value,
								LineNo = (int)log.LineNo,
								ClientCode = log.ClientCode,
								TransNo = log.TransNo,
								DiscReasonCode = log.DiscReasonCode,
								NettSelling = log.NettSelling,
								TransactionDate = log.TransactionDate,
								TransactionTime = log.TransactionTime
							})
							.ToArray();

						// 6) combine layby items and transaction logs first
						TranslogDto[] rawCombinedLogs = itemLogDtos.Concat(existingLogDtos).ToArray();

						// 7) Aggregate ALL items (layby + transaction) to handle removed items properly
						var aggregatedItems = AggregateLaybyItemsForReprint(rawCombinedLogs);

						// 8) Convert prior payments to have negative selling prices
						var formattedPriorPayments = priorPaymentLines.Select(ll => new TranslogDto
						{
							StyleCode = ll.StyleCode,
							ColourCode = ll.ColourCode,
							SizeCode = ll.SizeCode,
							Quantity = ll.Quantity.Value,
							SellingPrice = -Math.Abs(ll.SellingPrice.Value), // Make negative for prior payments
							LineNo = (int)ll.LineNo,
							TransactionDate = receiptTransaction.SaleDateTime,
							TransactionTime = receiptTransaction.SaleDateTime.ToString("HH:mm:ss")
						}).ToArray();

						// 9) Final combined logs with aggregated items and prior payments
						TranslogDto[] combinedLogs = aggregatedItems.Concat(formattedPriorPayments).ToArray();

						// 10) map your payments into TranspayDto[]
						TranspayDto[] combinedPays = (receiptTransaction.pays ?? Enumerable.Empty<Transpay>())
							.Select(p => new TranspayDto
							{
								PaymentType = p.PaymentType,
								PayAmount = p.PayAmount.Value,
								VoucherNumber = p.VoucherNumber,
								TransNo = p.TransNo
							})
							.ToArray();

						// Use aggregated items to get correct total cost
						var aggregatedItemsForTotal = AggregateLaybyItems(combinedLogs);
						float totalCost = aggregatedItemsForTotal.Sum(l => l.NettSelling.HasValue
							? l.NettSelling.Value
							: l.Quantity * l.SellingPrice);

						float totalPaid = combinedPays.Sum(p => p.PayAmount);
						float remainingBalance = totalCost - totalPaid;

						// 11) Use the layby edit detection that was done earlier
						byte[] pdfAttachment;
						if (isLaybyEdit)
						{
							// Use layby edit receipt format
							pdfAttachment = GenerateLaybyEditPdf(
								receiptTransaction,
								titleArray,
								headerArray,
								footerArray,
								laybyMessages,
								combinedLogs,
								combinedPays,
								transNoRequest.TransNo.Value,
								amountDue: remainingBalance,
								change: 0,
								staffLine,
								isReprint: true
							);
						}
						else
						{
							// Use regular layby receipt format
							pdfAttachment = GenerateLaybyReceiptPdf(
								receiptTransaction,
								titleArray,
								headerArray,
								footerArray,
								laybyMessages,
								combinedLogs,
								combinedPays,
								transNoRequest.TransNo.Value,
								amountDue: remainingBalance,
								change: 0,
								staffLine,
								isReprint: true
							);
						}

						await _emailService.SendReceiptEmailAsync(
							transNoRequest.Email,
							_storeTimeContext.StoreLocalTime,
							subject,
							pdfAttachment,
							title);

						return Ok();
					}
					else
					{
						return NotFound("Layby code not found for the provided TransNo.");
					}
				}
				else
				{
					// Handle the case where TransNo is null
					throw new ArgumentNullException(nameof(transNoRequest.TransNo), "Transaction number cannot be null.");
				}
			}
			else
			{
				if (receiptTransaction.TransType == 2)
				{
					if (transNoRequest.TransNo.HasValue)
					{
						// 1) fetch your layby lines
						string laybyCode = await _laybyRepository.GetLaybyCodeByTransNo(transNoRequest.TransNo.Value);
						if (laybyCode != null)
						{
							IEnumerable<Laybyline> laybyLines = await _laybySearchRepository.GetLaybyLines(laybyCode);
							TranslogDto[] laybyLogDtos = (laybyLines ?? Enumerable.Empty<Laybyline>())
							// drop exactly the last laybyLine
							.SkipLast(1)
							// now project each of the remaining ones
							.Select(ll => new TranslogDto
							{
								StyleCode = ll.StyleCode,
								ColourCode = ll.ColourCode,
								SizeCode = ll.SizeCode,
								Quantity = ll.Quantity.Value,
								SellingPrice = ll.SellingPrice.Value,
								LineNo = (int)ll.LineNo,
								TransactionDate = receiptTransaction.SaleDateTime,
								TransactionTime = receiptTransaction.SaleDateTime.ToString("HH:mm:ss")
								// …and any other fields…
							})
							.ToArray();


							// 3) map existing domain-logs → TranslogDto[]
							TranslogDto[] existingLogDtos = (receiptTransaction.logs ?? Enumerable.Empty<Translog>())
								.Select(log => new TranslogDto
								{
									StyleCode = log.StyleCode,
									ColourCode = log.ColourCode,
									SizeCode = log.SizeCode,
									Quantity = log.Quantity.Value,
									SellingPrice = log.SellingPrice.Value,
									LineNo = (int)log.LineNo,
									ClientCode = log.ClientCode,
									TransNo = log.TransNo,
									DiscReasonCode = log.DiscReasonCode,
									NettSelling = log.NettSelling,
									TransactionDate = log.TransactionDate,
									TransactionTime = log.TransactionTime
								})
								.ToArray();

							// 4) combine exactly like the TS spread [...laybyLines, ...existingLogs]
							TranslogDto[] combinedLogs = laybyLogDtos
								.Concat(existingLogDtos)
								.ToArray();

							// 5) map your payments into TranspayDto[]
							TranspayDto[] combinedPays = (receiptTransaction.pays ?? Enumerable.Empty<Transpay>())
								.Select(p => new TranspayDto
								{
									PaymentType = p.PaymentType,
									PayAmount = p.PayAmount.Value,
									VoucherNumber = p.VoucherNumber,
									TransNo = p.TransNo
								})
								.ToArray();

							float totalCost = combinedLogs
								.Where(l => l.Quantity > 0 && l.SellingPrice > 0)
								.Sum(l => l.NettSelling.HasValue
								? l.NettSelling.Value
								: l.Quantity * l.SellingPrice);

							float totalPaid = combinedPays.Sum(p => p.PayAmount);
							float remainingBalance = totalCost - totalPaid;

							// 6) now call your PDF generator with the DTO arrays
							byte[] pdfAttachment = GenerateLaybyReturnPdf(
								receiptTransaction,
								titleArray,
								headerArray,
								footerArray,
								laybyMessages,
								combinedLogs,
								combinedPays,
								transNoRequest.TransNo.Value,
								amountDue: remainingBalance,
								change: 0,
								staffLine,
								isReprint: true,
								laybyCode: laybyCode
							);


							await _emailService.SendReceiptEmailAsync(
								transNoRequest.Email,
								_storeTimeContext.StoreLocalTime,
								subject,
								pdfAttachment,
								title);

							return Ok();
						}
						else
						{
							byte[] pdfAttachment = GenerateReceiptPdf(receiptTransaction, titleArray, headerArray, footerArray, staffLine, emailRequest, giftVoucherDetails, creditNoteDetails, laybyMessages, isReprint: true);

							await _emailService.SendReceiptEmailAsync(
								transNoRequest.Email,
								_storeTimeContext.StoreLocalTime,
								subject,
								pdfAttachment,
								title);

							return Ok();
						}
					}
					else
					{
						// Handle the case where TransNo is null (e.g., throw an exception or return an error response)
						throw new ArgumentNullException(nameof(transNoRequest.TransNo), "Transaction number cannot be null.");
					}
				}
				else if (receiptTransaction.TransType == 9) // Layby Cancellation
				{
					if (transNoRequest.TransNo.HasValue)
					{
						// 1) fetch your layby lines
						string laybyCode = await _laybyRepository.GetLaybyCodeByTransNo(transNoRequest.TransNo.Value);
						if (laybyCode != null)
						{
							IEnumerable<Laybyline> laybyLines = await _laybySearchRepository.GetLaybyLines(laybyCode);

							// 2) map Laybyline → TranslogDto (exclude the last layby line which is the cancellation entry)
							TranslogDto[] laybyLogDtos = (laybyLines ?? Enumerable.Empty<Laybyline>())
								.SkipLast(1) // Skip the cancellation entry
								.Select(ll => new TranslogDto
								{
									StyleCode = ll.StyleCode,
									ColourCode = ll.ColourCode,
									SizeCode = ll.SizeCode,
									Quantity = ll.Quantity.Value,
									SellingPrice = ll.SellingPrice.Value,
									LineNo = (int)ll.LineNo,
									TransactionDate = receiptTransaction.SaleDateTime,
									TransactionTime = receiptTransaction.SaleDateTime.ToString("HH:mm:ss")
								})
								.ToArray();

							// 3) map existing domain-logs → TranslogDto[]
							TranslogDto[] existingLogDtos = (receiptTransaction.logs ?? Enumerable.Empty<Translog>())
								.Select(log => new TranslogDto
								{
									StyleCode = log.StyleCode,
									ColourCode = log.ColourCode,
									SizeCode = log.SizeCode,
									Quantity = log.Quantity.Value,
									SellingPrice = log.SellingPrice.Value,
									LineNo = (int)log.LineNo,
									ClientCode = log.ClientCode,
									TransNo = log.TransNo,
									DiscReasonCode = log.DiscReasonCode,
									NettSelling = log.NettSelling,
									TransactionDate = log.TransactionDate,
									TransactionTime = log.TransactionTime
								})
								.ToArray();

							// 4) combine layby items with current transaction logs
							TranslogDto[] combinedLogs = laybyLogDtos
								.Concat(existingLogDtos)
								.ToArray();

							// 5) map your payments into TranspayDto[]
							TranspayDto[] combinedPays = (receiptTransaction.pays ?? Enumerable.Empty<Transpay>())
								.Select(p => new TranspayDto
								{
									PaymentType = p.PaymentType,
									PayAmount = p.PayAmount.Value,
									VoucherNumber = p.VoucherNumber,
									TransNo = p.TransNo
								})
								.ToArray();

							// 6) Generate layby cancellation PDF
							byte[] pdfAttachment = GenerateLaybyReturnPdf(
								receiptTransaction,
								titleArray,
								headerArray,
								footerArray,
								laybyMessages,
								combinedLogs,
								combinedPays,
								transNoRequest.TransNo.Value,
								amountDue: 0, // For cancellations, amount due is 0
								change: 0,
								staffLine,
								isReprint: true,
								laybyCode: laybyCode
							);

							await _emailService.SendReceiptEmailAsync(
								transNoRequest.Email,
								_storeTimeContext.StoreLocalTime,
								subject,
								pdfAttachment,
								title);

							return Ok();
						}
						else
						{
							// Fallback if layby code not found
							byte[] pdfAttachment = GenerateReceiptPdf(receiptTransaction, titleArray, headerArray, footerArray, staffLine, emailRequest, giftVoucherDetails, creditNoteDetails, laybyMessages, isReprint: true);

							await _emailService.SendReceiptEmailAsync(
								transNoRequest.Email,
								_storeTimeContext.StoreLocalTime,
								subject,
								pdfAttachment,
								title);

							return Ok();
						}
					}
					else
					{
						// Handle the case where TransNo is null
						throw new ArgumentNullException(nameof(transNoRequest.TransNo), "Transaction number cannot be null.");
					}
				}
				else
				{
					byte[] pdfAttachment = GenerateReceiptPdf(receiptTransaction, titleArray, headerArray, footerArray, staffLine, emailRequest, giftVoucherDetails, creditNoteDetails, laybyMessages, isReprint: true);

					await _emailService.SendReceiptEmailAsync(
						transNoRequest.Email,
						_storeTimeContext.StoreLocalTime,
						subject,
						pdfAttachment,
						title);

					return Ok();
				}
			}
		}
		catch (Exception ex)
		{
			return StatusCode(500, $"Internal server error: {ex.Message}");
		}
	}

	[HttpPost("send-receipt")]
	public async Task<IActionResult> SendReceipt([FromBody] EmailRequest emailRequest)
	{
		if (string.IsNullOrWhiteSpace(emailRequest.Email))
		{
			return BadRequest("Email is required.");
		}

		try
		{
			string pinPadNo = _staffCodeContext.PINPadNo;
			string storeId = _staffCodeContext.StoreDetailsDto.StoreId;
			string staffLine = $"Served by {await _receiptPrintingRepository.StaffName(_staffCodeContext.StaffCode)}"; // not from history staffContext should be fine

			var titleArray = await _houseKeepingRepository.GetReceiptTitle(
				_storeTimeContext.StoreLocalTime.Date, storeId, pinPadNo);
			string title = titleArray?.FirstOrDefault()?.RecLine ?? "";
			var headerArray = await _houseKeepingRepository.GetReceiptHeader(
				_storeTimeContext.StoreLocalTime.Date, storeId, pinPadNo);
			var footerArray = await _houseKeepingRepository.GetReceiptFooter(
				_storeTimeContext.StoreLocalTime.Date, storeId, pinPadNo);

			// *** New: Check SYSCONTROL configuration ***
			IEnumerable<ReceiptLayby> laybyMessages = null;
			var sysControl = await _houseKeepingRepository.GetSysStatus(storeId);
			if (sysControl != null &&
				sysControl.LAYBY_NUMBER_OF_WEEKS?.ToUpper() == "T" &&
				(emailRequest.ReceiptDto.TransType == 4 || emailRequest.ReceiptDto.TransType == 5))
			{
				laybyMessages = await _houseKeepingRepository.GetReceiptLayby(storeId);
			}

			var giftVoucherDetails = new List<Giftvoucher>();
			var creditNoteDetails = new List<Creditnote>();
			if (emailRequest.ReceiptDto.pays != null)
			{
				foreach (var transPay in emailRequest.ReceiptDto.pays)
				{
					if (transPay.PaymentType == "Credit Note")
					{

						var results = await _crnRepository.SearchUnredeemed(transPay.VoucherNumber);

						if (results == null)
						{
							results = await _crnRepository
								.SearchRedeemed(transPay.VoucherNumber);
						}

						if (results != null)
						{
							creditNoteDetails.Add(results);
						}
					}
					else
					{
						var results = await _giftVoucherRepository
							.SearchUnredeemed(transPay.VoucherNumber);

						// if none, try redeemed
						if (results == null)
						{
							results = await _giftVoucherRepository
								.SearchRedeemed(transPay.VoucherNumber);
						}

						// if we got anything, add them
						if (results != null)
						{
							giftVoucherDetails.Add(results);
						}
					}
				}
			}

			(string subject, string htmlMessage) = GetEmailTemplate(emailRequest.ReceiptDto.TransType);
			byte[] pdfAttachment = GenerateReceiptPdf(emailRequest.ReceiptDto, titleArray, headerArray, footerArray, staffLine, emailRequest, giftVoucherDetails, creditNoteDetails, laybyMessages, isReprint: false, emailRequest.orderCode, emailRequest.accountCode, emailRequest.accountName);
			await _emailService.SendReceiptEmailAsync(emailRequest.Email, DateTime.Now, subject, pdfAttachment, title);

			return Ok();
		}
		catch (Exception ex)
		{
			return StatusCode(500, $"Internal server error: {ex.Message}");
		}
	}

	[HttpPost("layby-payment-receipt")]
	public async Task<IActionResult> SendLaybyPaymentReceipt([FromBody] LaybyPaymentEmailRequest laybyPaymentEmailRequest)
	{
		if (string.IsNullOrWhiteSpace(laybyPaymentEmailRequest.Email))
		{
			return BadRequest("Email is required.");
		}

		try
		{
			string pinPadNo = _staffCodeContext.PINPadNo;
			string storeId = _staffCodeContext.StoreDetailsDto.StoreId;
			string staffLine = $"Served by {await _receiptPrintingRepository.StaffName(_staffCodeContext.StaffCode)}"; // not from history staffContext should be fine

			var titleArray = await _houseKeepingRepository.GetReceiptTitle(
				_storeTimeContext.StoreLocalTime.Date, storeId, pinPadNo);
			string title = titleArray?.FirstOrDefault()?.RecLine ?? "";
			var headerArray = await _houseKeepingRepository.GetReceiptHeader(
				_storeTimeContext.StoreLocalTime.Date, storeId, pinPadNo);
			var footerArray = await _houseKeepingRepository.GetReceiptFooter(
				_storeTimeContext.StoreLocalTime.Date, storeId, pinPadNo);

			// *** New: Check SYSCONTROL configuration ***
			IEnumerable<ReceiptLayby> laybyMessages = null;
			var sysControl = await _houseKeepingRepository.GetSysStatus(storeId);
			if (sysControl != null &&
				sysControl.LAYBY_NUMBER_OF_WEEKS?.ToUpper() == "T" &&
				(laybyPaymentEmailRequest.ReceiptDto.TransType == 4 || laybyPaymentEmailRequest.ReceiptDto.TransType == 5))
			{
				laybyMessages = await _houseKeepingRepository.GetReceiptLayby(storeId);
			}

			// Check if this is actually a layby edit (transType 4 but with recent transType 5/7 lines)
			bool isLaybyEdit = false;

			// For layby transactions (transType 4), check if it's actually an edit by examining layby lines
			if (laybyPaymentEmailRequest.ReceiptDto.TransType == 4 && laybyPaymentEmailRequest.transNo > 0)
			{
				try
				{
					string laybyCode = await _laybyRepository.GetLaybyCodeByTransNo(laybyPaymentEmailRequest.transNo);
					if (!string.IsNullOrEmpty(laybyCode))
					{
						IEnumerable<Laybyline> laybyLines = await _laybySearchRepository.GetLaybyLines(laybyCode);
						isLaybyEdit = IsLaybyEditFromLaybyLines(laybyLines);
					}
				}
				catch (Exception)
				{
					// If we can't fetch layby lines, fall back to checking the logs parameter
					// which should contain the layby history if properly passed from frontend
					isLaybyEdit = IsLaybyEdit(laybyPaymentEmailRequest.ReceiptDto);
				}
			}
			else
			{
				// For non-layby transactions or when transNo is not available, use the original method
				isLaybyEdit = IsLaybyEdit(laybyPaymentEmailRequest.ReceiptDto);
			}

			string subject;
			byte[] pdfAttachment;

			if (isLaybyEdit)
			{
				// Use layby edit receipt format
				subject = "Your Layby Edit Receipt from SoleMatrixPOS";
				pdfAttachment = GenerateLaybyEditPdf(laybyPaymentEmailRequest.ReceiptDto, titleArray, headerArray, footerArray, laybyMessages, laybyPaymentEmailRequest.logs, laybyPaymentEmailRequest.pays, laybyPaymentEmailRequest.transNo, laybyPaymentEmailRequest.amountDue, laybyPaymentEmailRequest.change, staffLine);
			}
			else
			{
				// Use regular layby receipt format
				(subject, string htmlMessage) = GetEmailTemplate(laybyPaymentEmailRequest.ReceiptDto.TransType);
				pdfAttachment = GenerateLaybyReceiptPdf(laybyPaymentEmailRequest.ReceiptDto, titleArray, headerArray, footerArray, laybyMessages, laybyPaymentEmailRequest.logs, laybyPaymentEmailRequest.pays, laybyPaymentEmailRequest.transNo, laybyPaymentEmailRequest.amountDue, laybyPaymentEmailRequest.change, staffLine);
			}

			await _emailService.SendReceiptEmailAsync(laybyPaymentEmailRequest.Email, DateTime.Now, subject, pdfAttachment, title);

			return Ok();
		}
		catch (Exception ex)
		{
			return StatusCode(500, $"Internal server error: {ex.Message}");
		}
	}

	[HttpPost("layby-return-receipt")]
	public async Task<IActionResult> SendLaybyReturnReceipt([FromBody] LaybyPaymentEmailRequest laybyPaymentEmailRequest)
	{
		if (string.IsNullOrWhiteSpace(laybyPaymentEmailRequest.Email))
		{
			return BadRequest("Email is required.");
		}

		try
		{
			string pinPadNo = _staffCodeContext.PINPadNo;
			string storeId = _staffCodeContext.StoreDetailsDto.StoreId;
			string staffLine = $"Served by {await _receiptPrintingRepository.StaffName(_staffCodeContext.StaffCode)}"; // not from history staffContext should be fine

			var titleArray = await _houseKeepingRepository.GetReceiptTitle(
				_storeTimeContext.StoreLocalTime.Date, storeId, pinPadNo);
			string title = titleArray?.FirstOrDefault()?.RecLine ?? "";
			var headerArray = await _houseKeepingRepository.GetReceiptHeader(
				_storeTimeContext.StoreLocalTime.Date, storeId, pinPadNo);
			var footerArray = await _houseKeepingRepository.GetReceiptFooter(
				_storeTimeContext.StoreLocalTime.Date, storeId, pinPadNo);

			IEnumerable<ReceiptLayby> laybyMessages = null;

			// Fetch layby data for return receipt (similar to reprint logic)
			string laybyCode = "N/A";
			TranslogDto[] combinedLogs = laybyPaymentEmailRequest.logs ?? new TranslogDto[0];

			// Fetch layby lines using transaction number
			if (laybyPaymentEmailRequest.transNo > 0)
			{
				try
				{
					string fetchedLaybyCode = await _laybyRepository.GetLaybyCodeByTransNo(laybyPaymentEmailRequest.transNo);
					if (!string.IsNullOrEmpty(fetchedLaybyCode))
					{
						laybyCode = fetchedLaybyCode;

						// Fetch layby lines to get the actual items
						IEnumerable<Laybyline> laybyLines = await _laybySearchRepository.GetLaybyLines(laybyCode);
						TranslogDto[] laybyLogDtos = (laybyLines ?? Enumerable.Empty<Laybyline>())
							.SkipLast(1) // Skip the last entry (return/cancellation entry)
							.Select(ll => new TranslogDto
							{
								StyleCode = ll.StyleCode,
								ColourCode = ll.ColourCode,
								SizeCode = ll.SizeCode,
								Quantity = ll.Quantity.Value,
								SellingPrice = ll.SellingPrice.Value,
								LineNo = (int)ll.LineNo,
								TransactionDate = laybyPaymentEmailRequest.ReceiptDto.SaleDateTime,
								TransactionTime = laybyPaymentEmailRequest.ReceiptDto.SaleDateTime.ToString("HH:mm:ss")
							})
							.ToArray();

						// Combine layby items with current transaction logs
						combinedLogs = laybyLogDtos.Concat(laybyPaymentEmailRequest.logs ?? Enumerable.Empty<TranslogDto>()).ToArray();
					}
				}
				catch (Exception ex)
				{
				}
			}

			(string subject, string htmlMessage) = GetEmailTemplate(laybyPaymentEmailRequest.ReceiptDto.TransType);
			byte[] pdfAttachment = GenerateLaybyReturnPdf(laybyPaymentEmailRequest.ReceiptDto, titleArray, headerArray, footerArray, laybyMessages, combinedLogs, laybyPaymentEmailRequest.pays, laybyPaymentEmailRequest.transNo, laybyPaymentEmailRequest.amountDue, laybyPaymentEmailRequest.change, staffLine, false, laybyCode);
			await _emailService.SendReceiptEmailAsync(laybyPaymentEmailRequest.Email, DateTime.Now, subject, pdfAttachment, title);

			return Ok();
		}
		catch (Exception ex)
		{
			return StatusCode(500, $"Internal server error: {ex.Message}");
		}
	}

	[HttpPost("layby-edit-receipt")]
	public async Task<IActionResult> SendLaybyEditReceipt([FromBody] LaybyPaymentEmailRequest laybyPaymentEmailRequest)
	{
		if (string.IsNullOrWhiteSpace(laybyPaymentEmailRequest.Email))
		{
			return BadRequest("Email is required.");
		}

		try
		{
			string pinPadNo = _staffCodeContext.PINPadNo;
			string storeId = _staffCodeContext.StoreDetailsDto.StoreId;
			string staffLine = $"Served by {await _receiptPrintingRepository.StaffName(_staffCodeContext.StaffCode)}";

			var titleArray = await _houseKeepingRepository.GetReceiptTitle(
				_storeTimeContext.StoreLocalTime.Date, storeId, pinPadNo);
			string title = titleArray?.FirstOrDefault()?.RecLine ?? "";
			var headerArray = await _houseKeepingRepository.GetReceiptHeader(
				_storeTimeContext.StoreLocalTime.Date, storeId, pinPadNo);
			var footerArray = await _houseKeepingRepository.GetReceiptFooter(
				_storeTimeContext.StoreLocalTime.Date, storeId, pinPadNo);

			// Check for layby messages if applicable
			IEnumerable<ReceiptLayby> laybyMessages = null;
			var sysControl = await _houseKeepingRepository.GetSysStatus(storeId);
			if (sysControl != null &&
				sysControl.LAYBY_NUMBER_OF_WEEKS?.ToUpper() == "T" &&
				laybyPaymentEmailRequest.ReceiptDto.TransType == 4)
			{
				laybyMessages = await _houseKeepingRepository.GetReceiptLayby(storeId);
			}

			// Use custom subject for layby edit receipts
			string subject = "Your Layby Edit Receipt from SoleMatrixPOS";
			byte[] pdfAttachment = GenerateLaybyEditPdf(laybyPaymentEmailRequest.ReceiptDto, titleArray, headerArray, footerArray, laybyMessages, laybyPaymentEmailRequest.logs, laybyPaymentEmailRequest.pays, laybyPaymentEmailRequest.transNo, laybyPaymentEmailRequest.amountDue, laybyPaymentEmailRequest.change, staffLine);
			await _emailService.SendReceiptEmailAsync(laybyPaymentEmailRequest.Email, DateTime.Now, subject, pdfAttachment, title);

			return Ok();
		}
		catch (Exception ex)
		{
			return StatusCode(500, $"Internal server error: {ex.Message}");
		}
	}

	// Update the email template to handle TransType 5 (Layby Payment)
	private (string subject, string htmlMessage) GetEmailTemplate(int transType)
	{
		return transType switch
		{
			1 => ("Your Receipt from SoleMatrixPOS", "<p>Thank you for your purchase. Here is your receipt.</p>"),
			2 => ("Your Return Receipt from SoleMatrixPOS", "<p>Your return has been processed. Here is your return receipt.</p>"),
			3 => ("Your Exchange Receipt from SoleMatrixPOS", "<p>Your exchange has been processed. Here is your exchange receipt.</p>"),
			4 => ("Your Layby Receipt from SoleMatrixPOS", "<p>Thank you for initiating your layby. Here is your layby receipt.</p>"),
			6 => ("Your Gift Card Receipt from SoleMatrixPOS", "<p>Thank you for your purchase. Here is your gift card receipt.</p>"),
			5 => ("Your Layby Payment Receipt from SoleMatrixPOS", "<p>Your layby payment has been processed. Here is your receipt.</p>"),
			9 => ("Your Layby Cancellation Receipt from SoleMatrixPOS", "<p>Your layby cancellation has been processed. Here is your receipt.</p>"),
			11 => ("Your Customer Order from SoleMatrixPOS", "<p>Thank you for your order. Here are your order details.</p>"),
			12 => ("Your Quote from SoleMatrixPOS", "<p>Here is your requested quote.</p>"),
			_ => throw new ArgumentException($"Invalid transaction type: {transType}")
		};
	}

	// Updated to accept optional laybyMessages

	private byte[] GenerateLaybyReceiptPdf(ReceiptTransactionDto receiptDto,
	IEnumerable<ReceiptTitle> titleArray,
	IEnumerable<ReceiptHeader> headerArray,
	IEnumerable<ReceiptFooter> footerArray,
	IEnumerable<ReceiptLayby> laybyMessages,
	TranslogDto[] translogs,
	TranspayDto[] transpays,
	int transNo,
	float amountDue,
	float change,
	string staffLine,
	bool isReprint = false)
	{
		string htmlContent = BuildLaybyHtmlContent(receiptDto, titleArray, headerArray, footerArray, laybyMessages, translogs, transpays, transNo, amountDue, change, staffLine, isReprint);

		var converter = new HtmlToPdf();
		converter.Options.MarginLeft = 0;
		converter.Options.MarginRight = 0;
		converter.Options.MarginTop = 5;
		converter.Options.MarginBottom = 5;

		var pdfDocument = converter.ConvertHtmlString(htmlContent);
		using (var memoryStream = new MemoryStream())
		{
			pdfDocument.Save(memoryStream);
			pdfDocument.Close();
			return memoryStream.ToArray();
		}
	}

	private byte[] GenerateLaybyReturnPdf(ReceiptTransactionDto receiptDto,
	IEnumerable<ReceiptTitle> titleArray,
	IEnumerable<ReceiptHeader> headerArray,
	IEnumerable<ReceiptFooter> footerArray,
	IEnumerable<ReceiptLayby> laybyMessages,
	TranslogDto[] translogs,
	TranspayDto[] transpays,
	int transNo,
	float amountDue,
	float change,
	string staffLine,
	bool isReprint = false,
	string laybyCode = null)
	{

		string htmlContent = BuildLaybyReturnHtmlContent(receiptDto, titleArray, headerArray, footerArray, laybyMessages, translogs, transpays, transNo, amountDue, change, staffLine, isReprint, laybyCode);

		var converter = new HtmlToPdf();
		converter.Options.MarginLeft = 0;
		converter.Options.MarginRight = 0;
		converter.Options.MarginTop = 5;
		converter.Options.MarginBottom = 5;

		var pdfDocument = converter.ConvertHtmlString(htmlContent);
		using (var memoryStream = new MemoryStream())
		{
			pdfDocument.Save(memoryStream);
			pdfDocument.Close();
			return memoryStream.ToArray();
		}
	}

	private byte[] GenerateLaybyEditPdf(ReceiptTransactionDto receiptDto,
	IEnumerable<ReceiptTitle> titleArray,
	IEnumerable<ReceiptHeader> headerArray,
	IEnumerable<ReceiptFooter> footerArray,
	IEnumerable<ReceiptLayby> laybyMessages,
	TranslogDto[] translogs,
	TranspayDto[] transpays,
	int transNo,
	float amountDue,
	float change,
	string staffLine,
	bool isReprint = false)
	{
		string htmlContent = BuildLaybyEditHtmlContent(receiptDto, titleArray, headerArray, footerArray, laybyMessages, translogs, transpays, transNo, amountDue, change, staffLine, isReprint);

		var converter = new HtmlToPdf();
		converter.Options.MarginLeft = 0;
		converter.Options.MarginRight = 0;
		converter.Options.MarginTop = 5;
		converter.Options.MarginBottom = 5;

		var pdfDocument = converter.ConvertHtmlString(htmlContent);
		using (var memoryStream = new MemoryStream())
		{
			pdfDocument.Save(memoryStream);
			pdfDocument.Close();
			return memoryStream.ToArray();
		}
	}

	private byte[] GenerateReceiptPdf(ReceiptTransactionDto receiptDto,
		IEnumerable<ReceiptTitle> titleArray,
		IEnumerable<ReceiptHeader> headerArray,
		IEnumerable<ReceiptFooter> footerArray,
		string staffLine,
		EmailRequest emailRequest,
		IEnumerable<Giftvoucher> giftVoucherDetails = null,
		IEnumerable<Creditnote> creditNoteDetails = null,
		IEnumerable<ReceiptLayby> laybyMessages = null,
		bool isReprint = false,
		string orderCode = null,
		string accountName = null,
		string accountCode = null)
	{
		string htmlContent = BuildHtmlContent(receiptDto, titleArray, headerArray, footerArray, emailRequest, laybyMessages, giftVoucherDetails, creditNoteDetails, staffLine, isReprint, orderCode, accountName, accountCode);

		var converter = new HtmlToPdf();
		converter.Options.MarginLeft = 0;
		converter.Options.MarginRight = 0;
		converter.Options.MarginTop = 5;
		converter.Options.MarginBottom = 5;

		var pdfDocument = converter.ConvertHtmlString(htmlContent);
		using (var memoryStream = new MemoryStream())
		{
			pdfDocument.Save(memoryStream);
			pdfDocument.Close();
			return memoryStream.ToArray();
		}
	}

	private string BuildLaybyHtmlContent(
		ReceiptTransactionDto receiptDto,
		IEnumerable<ReceiptTitle> title,
		IEnumerable<ReceiptHeader> headers,
		IEnumerable<ReceiptFooter> footers,
		IEnumerable<ReceiptLayby> laybyMessages,
		TranslogDto[] logs,
		TranspayDto[] pays,
		int transno,
		float amountDue,
		float change,
		string staffLine,
		bool isReprint = false)
	{
		string baseDirectory = AppContext.BaseDirectory;
		string templatePath = Path.Combine(baseDirectory, "Email", "ReceiptTemplate.html");
		if (!System.IO.File.Exists(templatePath))
		{
			throw new FileNotFoundException($"Template file not found at: {templatePath}");
		}
		var template = System.IO.File.ReadAllText(templatePath);

		// Build the layby message HTML (if any)
		string laybyHtml = "";
		if (laybyMessages != null && laybyMessages.Any())
		{
			laybyHtml = "<div class='layby-message'>" +
						string.Join("<br>", laybyMessages.Select(l => l.RecLine)) +
						"</div>";
		}

		// Replace the placeholders in the template.
		// Note: Ensure your HTML template contains a placeholder like {{LAYBY_ITEM_ROWS}}
		return template
			.Replace("{{REPRINT_NOTICE}}", isReprint ?
				"<div class='reprint-notice'>** REPRINTED RECEIPT **</div>" : "")
			.Replace("{{TITLE_LINES}}", string.Join("<br>", title.Select(t => t.RecLine)))
			.Replace("{{HEADER_LINES}}", string.Join("<br>", headers.Select(h => h.RecLine)))
			.Replace("{{ORDER_CODE_SECTION}}", "")
			.Replace("{{TRANSACTION_TYPE}}", GetTransactionTypeHeader(receiptDto.TransType))
			.Replace("{{CUSTOMER_DETAILS}}", "")
			.Replace("{{ITEM_HEADERS}}", GetItemHeaders(receiptDto.TransType))
			.Replace("{{ITEMS}}", GenerateLaybyItemRows(receiptDto, logs))
			.Replace("{{QUOTE_DISCLAIMER}}", GetQuoteDisclaimer(receiptDto.TransType))
			.Replace("{{TOTAL_DETAILS}}", GenerateTotalDetails(receiptDto))
			.Replace("{{TRANSACTION_DATE}}", receiptDto.SaleDateTime.ToString("dd/MM/yy hh:mm:ss tt"))
			.Replace("{{STAFF_NAME}}", staffLine)
			.Replace("{{FOOTER_LINES}}", string.Join("<br>", footers.Select(f => f.RecLine)))
			// Insert layby message below the sale footer
			.Replace("{{LAYBY_MESSAGE}}", laybyHtml)
			// Insert new layby item rows (prior payments, current payments and remaining balance)
			.Replace("{{LAYBY_ITEM_ROWS}}", GenerateLaybyPriorPayments(logs, pays, amountDue, change))
			.Replace("{{VOUCHER_DETAILS}}", "");
	}

	private string BuildLaybyReturnHtmlContent(
		ReceiptTransactionDto receiptDto,
		IEnumerable<ReceiptTitle> title,
		IEnumerable<ReceiptHeader> headers,
		IEnumerable<ReceiptFooter> footers,
		IEnumerable<ReceiptLayby> laybyMessages,
		TranslogDto[] logs,
		TranspayDto[] pays,
		int transno,
		float amountDue,
		float change,
		string staffLine,
		bool isReprint = false,
		string laybyCode = null)
	{
		string baseDirectory = AppContext.BaseDirectory;
		string templatePath = Path.Combine(baseDirectory, "Email", "ReceiptTemplate.html");
		if (!System.IO.File.Exists(templatePath))
		{
			throw new FileNotFoundException($"Template file not found at: {templatePath}");
		}
		var template = System.IO.File.ReadAllText(templatePath);

		// Build the layby message HTML (if any)
		string laybyHtml = "";
		if (laybyMessages != null && laybyMessages.Any())
		{
			laybyHtml = "<div class='layby-message'>" +
						string.Join("<br>", laybyMessages.Select(l => l.RecLine)) +
						"</div>";
		}

		// Extract the layby code for display - use provided laybyCode or extract from logs
		// Layby code entries have quantity 0 and represent the layby code itself
		string displayLaybyCode = laybyCode ?? logs.FirstOrDefault(log => IsLaybyCodeEntry(log))?.StyleCode ?? "N/A";

		// Replace the placeholders in the template.
		return template
			.Replace("{{REPRINT_NOTICE}}", isReprint ?
				"<div class='reprint-notice'>** REPRINTED RECEIPT **</div>" : "")
			.Replace("{{TITLE_LINES}}", string.Join("<br>", title.Select(t => t.RecLine)))
			.Replace("{{HEADER_LINES}}", string.Join("<br>", headers.Select(h => h.RecLine)))
			.Replace("{{ORDER_CODE_SECTION}}", "")
			.Replace("{{CUSTOMER_DETAILS}}", "")
			.Replace("{{TRANSACTION_TYPE}}", GetTransactionTypeHeader(receiptDto.TransType))
			.Replace("{{ITEM_HEADERS}}", GetItemHeaders(receiptDto.TransType))
			.Replace("{{ITEMS}}", GenerateLaybyReturnItemRows(receiptDto, logs, displayLaybyCode))
			.Replace("{{QUOTE_DISCLAIMER}}", GetQuoteDisclaimer(receiptDto.TransType))
			.Replace("{{TOTAL_DETAILS}}", " ")
			.Replace("{{TRANSACTION_DATE}}", receiptDto.SaleDateTime.ToString("dd/MM/yy hh:mm:ss tt"))
			.Replace("{{STAFF_NAME}}", staffLine)
			.Replace("{{FOOTER_LINES}}", string.Join("<br>", footers.Select(f => f.RecLine)))
			// Insert layby message below the sale footer
			.Replace("{{LAYBY_MESSAGE}}", laybyHtml)
			// Insert new layby item rows (prior payments, current payments and remaining balance)
			.Replace("{{LAYBY_ITEM_ROWS}}", GenerateLaybyReturnPriorPayments(logs, pays, amountDue, change))
			.Replace("{{VOUCHER_DETAILS}}", "");
	}

	private string BuildLaybyEditHtmlContent(
		ReceiptTransactionDto receiptDto,
		IEnumerable<ReceiptTitle> title,
		IEnumerable<ReceiptHeader> headers,
		IEnumerable<ReceiptFooter> footers,
		IEnumerable<ReceiptLayby> laybyMessages,
		TranslogDto[] logs,
		TranspayDto[] pays,
		int transno,
		float amountDue,
		float change,
		string staffLine,
		bool isReprint = false)
	{

		string templatePath = Path.Combine(Directory.GetCurrentDirectory(), "Email", "LaybyEditReceiptTemplate.html");

		string template = System.IO.File.ReadAllText(templatePath);

		// Build the layby message HTML (if any)
		string laybyHtml = "";
		if (laybyMessages != null && laybyMessages.Any())
		{
			laybyHtml = "<div class='layby-message'>" +
						string.Join("<br>", laybyMessages.Select(l => l.RecLine)) +
						"</div>";
		}

		// Extract the layby code for display
		var laybyCode = logs.FirstOrDefault(log => IsLaybyCodeEntry(log))?.StyleCode ?? "N/A";

		// Use receiptDto.logs which contains Translog objects with transType information
		var laybyLogs = receiptDto.logs?.ToArray() ?? new Translog[0];

		// Replace the placeholders in the template
		return template
			.Replace("{{REPRINT_NOTICE}}", isReprint ?
				"<div class='reprint-notice'>** REPRINTED RECEIPT **</div>" : "")
			.Replace("{{TITLE_LINES}}", string.Join("<br>", title.Select(t => t.RecLine)))
			.Replace("{{HEADER_LINES}}", string.Join("<br>", headers.Select(h => h.RecLine)))
			.Replace("{{ORDER_CODE_SECTION}}", "")
			.Replace("{{TRANSACTION_TYPE}}", "LAYBY EDIT RECEIPT")
			.Replace("{{CUSTOMER_DETAILS}}", "")
			.Replace("{{LAYBY_CODE}}", laybyCode)
			.Replace("{{ORIGINAL_ITEMS_SECTION}}", GenerateOriginalLaybyItemsSection(laybyLogs))
			.Replace("{{ADDED_ITEMS_SECTION}}", GenerateAddedLaybyItemsSection(laybyLogs))
			.Replace("{{REMOVED_ITEMS_SECTION}}", GenerateRemovedLaybyItemsSection(laybyLogs))
			.Replace("{{QUOTE_DISCLAIMER}}", GetQuoteDisclaimer(receiptDto.TransType))
			.Replace("{{TOTAL_DETAILS}}", GenerateLaybyEditTotalDetails(logs, pays, amountDue))
			.Replace("{{TRANSACTION_DATE}}", receiptDto.SaleDateTime.ToString("dd/MM/yy hh:mm:ss tt"))
			.Replace("{{STAFF_NAME}}", staffLine)
			.Replace("{{FOOTER_LINES}}", string.Join("<br>", footers.Select(f => f.RecLine)))
			.Replace("{{LAYBY_MESSAGE}}", laybyHtml)
			.Replace("{{VOUCHER_DETAILS}}", "");
	}

	/// <summary>
	/// Generates HTML rows for a layby receipt including previous payments, current payments and the remaining balance.
	/// </summary>
	/// <param name="logs">Array of transaction logs (used for previous payments and total calculation).</param>
	/// <param name="pays">Array of current payments.</param>
	/// <param name="amountDue">The original amount due for the layby (may be recalculated).</param>
	/// <returns>A string containing HTML formatted rows.</returns>
	private string GenerateLaybyPriorPayments(TranslogDto[] logs, TranspayDto[] pays, float amountDue, float change)
	{
		var sb = new StringBuilder();

		// Prior Payments Section (negative values from logs)
		var priorPayments = logs.Where(log => log.Quantity != 0 && log.SellingPrice < 0).ToList();
		if (priorPayments.Any())
		{
			sb.Append("<div class='layby-prior-payments'>");
			sb.Append("<p><strong>Prior Payments:</strong></p>");
			sb.Append("<table class='reasons-table'>");
			sb.Append("<tr><th style='text-align:left;'>Payment</th><th style='text-align:right;'>Amount</th></tr>");
			foreach (var payment in priorPayments)
			{
				float paymentAmount = Math.Abs(payment.SellingPrice);
				sb.AppendFormat("<tr><td>{0}</td><td style='text-align:right;'>{1}</td></tr>",
					payment.StyleCode, paymentAmount.ToString("F2"));
			}
			sb.Append("</table>");
			sb.Append("</div>");
		}

		// Current Payments Section (excluding negative values)
		if (pays != null && pays.Any())
		{
			sb.Append("<div class='layby-current-payments'>");
			sb.Append("<p><strong>Current Payments:</strong></p>");
			sb.AppendFormat("<p><strong>Date:</strong> {0}</p>", DateTime.Now.ToString("dd/MM/yyyy"));

			sb.Append("<table class='reasons-table'>");
			sb.Append("<tr><th style='text-align:left;'>Payment Type</th><th style='text-align:right;'>Amount</th></tr>");

			float totalCashPaid = 0;
			foreach (var pay in pays)
			{
				if (pay.PayAmount > 0)
				{
					totalCashPaid += pay.PayAmount;
					sb.AppendFormat("<tr><td>{0}</td><td style='text-align:right;'>{1}</td></tr>",
						pay.PaymentType, pay.PayAmount.ToString("F2"));
				}
			}

			// Add the change to the cash payment to reflect total given by the customer
			if (change > 0)
			{
				totalCashPaid += change;
				sb.AppendFormat("<tr><td>Total Cash Given</td><td style='text-align:right;'>{0}</td></tr>", totalCashPaid.ToString("F2"));
				sb.AppendFormat("<tr><td>Change Returned</td><td style='text-align:right;'>{0}</td></tr>", change.ToString("F2"));
			}

			sb.Append("</table>");
			sb.Append("</div>");
		}

		// Remaining Balance Calculation - recalculate using aggregated items
		var aggregatedItems = AggregateLaybyItems(logs);
		float totalItemsCost = aggregatedItems.Sum(log => log.SellingPrice * log.Quantity);

		// Calculate total prior payments (negative selling prices in logs)
		// Only count prior payments with non-zero quantity to avoid counting layby code entries
		var priorPaymentEntries = logs.Where(log => log.Quantity != 0 && log.SellingPrice < 0).ToList();
		float totalPriorPayments = Math.Abs(priorPaymentEntries.Sum(log => log.SellingPrice));

		// Calculate current payments
		float currentPayments = pays.Sum(p => p.PayAmount);

		// Remaining balance = Total items cost - Prior payments - Current payments
		float remainingAmount = Math.Max(totalItemsCost - totalPriorPayments - currentPayments, 0);
		sb.AppendFormat("<p><strong>Remaining Balance:</strong> {0}</p>", remainingAmount.ToString("F2"));

		if (remainingAmount < 0.01)
		{
			sb.Append("<p style='color:green;'><strong>*** PAID IN FULL ***</strong></p>");
		}

		return sb.ToString();
	}

	private string GenerateLaybyReturnPriorPayments(TranslogDto[] logs, TranspayDto[] pays, float amountDue, float change)
	{
		var sb = new StringBuilder();

		// Prior Payments Section
		// For layby returns/cancellations, prior payments might not be in the logs as negative values
		// They should be calculated from the total item cost vs current refund
		var priorPayments = logs.Where(log => log.Quantity != 0 && log.SellingPrice < 0).ToList();

		// Calculate total item cost for layby returns (use same aggregation as item display)
		var aggregatedItems = AggregateLaybyItemsForReprint(logs);
		var itemCost = aggregatedItems.Sum(item => item.Quantity * item.SellingPrice);

		var currentRefund = pays?.Sum(p => Math.Abs(p.PayAmount)) ?? 0;

		// Calculate actual prior payments from negative price entries (payment logs)
		var actualPriorPayments = Math.Abs(logs.Where(log => log.SellingPrice < 0).Sum(log => log.SellingPrice));

		// Display prior payments - either from logs or estimated
		if (priorPayments.Any())
		{
			sb.Append("<div class='layby-prior-payments'>");
			sb.Append("<p><strong>Prior Payments:</strong></p>");
			sb.Append("<table class='reasons-table'>");
			sb.Append("<tr><th style='text-align:left;'>Payment</th><th style='text-align:right;'>Amount</th></tr>");
			foreach (var payment in priorPayments)
			{
				float paymentAmount = Math.Abs(payment.SellingPrice);
				sb.AppendFormat("<tr><td>{0}</td><td style='text-align:right;'>{1}</td></tr>",
					payment.StyleCode, paymentAmount.ToString("F2"));
			}
			sb.Append("</table>");
			sb.Append("</div>");
		}
		else if (actualPriorPayments > 0)
		{
			// Show actual prior payments when not found as individual entries
			sb.Append("<div class='layby-prior-payments'>");
			sb.Append("<p><strong>Prior Payments:</strong></p>");
			sb.Append("<table class='reasons-table'>");
			sb.Append("<tr><th style='text-align:left;'>Payment</th><th style='text-align:right;'>Amount</th></tr>");
			sb.AppendFormat("<tr><td>Previous Payments</td><td style='text-align:right;'>{0}</td></tr>",
				actualPriorPayments.ToString("F2"));
			sb.Append("</table>");
			sb.Append("</div>");
		}

		// Current Payments Section - show refunds (negative amounts)
		if (pays != null && pays.Any())
		{
			sb.Append("<div class='layby-current-payments'>");
			sb.Append("<p><strong>Current Refund:</strong></p>");
			sb.AppendFormat("<p><strong>Date:</strong> {0}</p>", DateTime.Now.ToString("dd/MM/yyyy"));

			sb.Append("<table class='reasons-table'>");
			sb.Append("<tr><th style='text-align:left;'>Payment Type</th><th style='text-align:right;'>Amount</th></tr>");

			float totalRefunded = 0;
			foreach (var pay in pays)
			{
				// For refunds, amounts are typically negative, so show absolute value
				float refundAmount = Math.Abs(pay.PayAmount);
				if (refundAmount > 0)
				{
					totalRefunded += refundAmount;
					sb.AppendFormat("<tr><td>{0}</td><td style='text-align:right;'>{1}</td></tr>",
						pay.PaymentType, refundAmount.ToString("F2"));
				}
			}

			sb.Append("</table>");
			sb.Append("</div>");
		}

		if (currentRefund >= actualPriorPayments && actualPriorPayments > 0)
		{
			sb.Append("<p style='color:green;'><strong>*** Fully Refunded ***</strong></p>");
		}
		else if (currentRefund > 0)
		{
			sb.Append("<p style='color:orange;'><strong>*** Partially Refunded ***</strong></p>");
		}
		else
		{
			sb.Append("<p style='color:red;'><strong>*** No Refund Returned ***</strong></p>");
		}

		return sb.ToString();
	}

	private string BuildHtmlContent(ReceiptTransactionDto receiptDto,
		IEnumerable<ReceiptTitle> title,
		IEnumerable<ReceiptHeader> headers,
		IEnumerable<ReceiptFooter> footers,
		EmailRequest emailrequest,
		IEnumerable<ReceiptLayby> laybyMessages,
		IEnumerable<Giftvoucher> giftVoucherDetails,
		IEnumerable<Creditnote> creditNoteDetails,
		string staffLine,
		bool isReprint = false,
		string orderCode = null,
		string accountName = null,
		string accountCode = null)
	{
		string baseDirectory = AppContext.BaseDirectory;
		string templatePath = Path.Combine(baseDirectory, "Email", "ReceiptTemplate.html");
		if (!System.IO.File.Exists(templatePath))
		{
			throw new FileNotFoundException($"Template file not found at: {templatePath}");
		}
		var template = System.IO.File.ReadAllText(templatePath);

		// Build the layby message HTML (if any)
		string laybyHtml = "";
		if (laybyMessages != null && laybyMessages.Any())
		{
			laybyHtml = "<div class='layby-message'>" +
						string.Join("<br>", laybyMessages.Select(l => l.RecLine)) +
						"</div>";
		}

		string laybyItemRows = "";

		string voucherSection = "";
		if (giftVoucherDetails?.Any() == true || creditNoteDetails?.Any() == true)
		{
			var sb = new StringBuilder();
			sb.AppendLine("<div class='voucher-list'>");
			sb.AppendLine("  <h2>Gift Vouchers Applied</h2>");
			sb.AppendLine("  <ul>");
			foreach (var v in giftVoucherDetails)
			{
				// adjust these fields to match your DTO
				sb.AppendLine(
				  $"    <li>Voucher #{v.GiftvoucherNo} — " +
				  $"Remaining: {v.GvValue:C}</li>");
			}
			foreach (var v in creditNoteDetails)
			{
				// adjust these fields to match your DTO
				sb.AppendLine(
				  $"    <li>Credit Note #{v.CreditNoteCode} — " +
				  $"Remaining: {v.CrnValue:C}</li>");
			}
			sb.AppendLine("  </ul>");
			sb.AppendLine("</div>");
			voucherSection = sb.ToString();
		}

		string giftSection = "";
		var giftCardSection = new StringBuilder();
		giftCardSection.AppendLine("<div class=\"customer-details\" style=\"text-align: center;\">");

		if (emailrequest != null)
		{
			if (!string.IsNullOrWhiteSpace(emailrequest.clientCode))
			{
				giftCardSection.AppendLine($"  <div class=\"label\">Client Code:</div><div class=\"value\">{emailrequest.clientCode}</div>");
				giftCardSection.AppendLine($"  <div class=\"label\">Client Name:</div><div class=\"value\">{emailrequest.clientName}</div>");
				giftCardSection.AppendLine($"  <div class=\"label\">Total Customer Points:</div><div class=\"value\">{emailrequest.customerPoints}</div>");
			}

			if (!string.IsNullOrWhiteSpace(emailrequest.pointsEarned))
			{
				giftCardSection.AppendLine($"  <div class=\"label\">Points Earned:</div><div class=\"value\">{emailrequest.pointsEarned}</div>");
			}
		}

		giftCardSection.AppendLine("</div>");
		giftSection = giftCardSection.ToString();

		string orderQuoteSection = "";
		if (!string.IsNullOrWhiteSpace(orderCode))
		{
			string label = receiptDto.TransType switch
			{
				11 => "Order",
				12 => "Quote",
				_ => null
			};

			if (!string.IsNullOrEmpty(label))
			{
				orderQuoteSection = $@"
			<div style='text-align: center; margin-bottom: 10px;'>
				<strong>{label}</strong><br>
				{label}#: {orderCode}
			</div>";
			}
		}

		return template
			.Replace("{{REPRINT_NOTICE}}", isReprint ?
				"<div class='reprint-notice'>** REPRINTED RECEIPT **</div>" : "")
			.Replace("{{TITLE_LINES}}", string.Join("<br>", title.Select(t => t.RecLine)))
			.Replace("{{HEADER_LINES}}", string.Join("<br>", headers.Select(h => h.RecLine)))
			.Replace("{{ORDER_CODE_SECTION}}", orderQuoteSection)
			.Replace("{{CUSTOMER_DETAILS}}", giftSection)
			.Replace("{{TRANSACTION_TYPE}}", GetTransactionTypeHeader(receiptDto.TransType))
			.Replace("{{ITEM_HEADERS}}", GetItemHeaders(receiptDto.TransType))
			.Replace("{{ITEMS}}", GenerateItemRows(receiptDto))
			.Replace("{{QUOTE_DISCLAIMER}}", GetQuoteDisclaimer(receiptDto.TransType))
			.Replace("{{TOTAL_DETAILS}}", GenerateTotalDetails(receiptDto, accountName, accountCode))
			.Replace("{{TRANSACTION_DATE}}", receiptDto.SaleDateTime.ToString("dd/MM/yy hh:mm:ss tt"))
			.Replace("{{STAFF_NAME}}", staffLine)
			.Replace("{{FOOTER_LINES}}", string.Join("<br>", footers.Select(f => f.RecLine)))
			.Replace("{{LAYBY_MESSAGE}}", laybyHtml)
			.Replace("{{LAYBY_ITEM_ROWS}}", laybyItemRows)
			.Replace("{{VOUCHER_DETAILS}}", voucherSection);
	}

	private string GenerateItemRows(ReceiptTransactionDto receiptDto)
	{
		var sb = new StringBuilder();
		var outsideReasons = new StringBuilder();

		var refs = receiptDto.refs ?? Enumerable.Empty<Transref>();

		foreach (var log in receiptDto.logs)
		{
			if (log.StyleCode == "Reason")
			{
				var reasonRefs = refs.Where(r => r.LineNo == log.LineNo).ToList();
				if (reasonRefs.Any())
				{
					foreach (var reasonRef in reasonRefs)
					{
						sb.Append($@"
                    <tr style='font-size: 12px;'>
                        <td colspan='5'> - Reason: {reasonRef.TransReference}</td>
                    </tr>");
					}
				}
				continue;
			}

			var price = FinancialRound(log.SellingPrice ?? 0);
			string rowClass = "";
			if (receiptDto.TransType == 2)
			{
				rowClass = "negative";
			}
			else if (receiptDto.TransType == 3 && log.Quantity < 0)
			{
				rowClass = "negative";
			}

			sb.Append($@"
        <tr class='{rowClass}'>
            <td>{log.StyleCode}</td>
            <td>{log.ColourCode}</td>
            <td>{log.SizeCode}</td>
            <td>{log.Quantity}</td>
            <td class='text-right'>{price}{(receiptDto.TransType == 12 ? "*" : "")}</td>
        </tr>");

			// Add description row if either description exists
			if (!string.IsNullOrEmpty(log.StockDescription) || !string.IsNullOrEmpty(log.ColourDescription))
			{
				string description = "";
				if (!string.IsNullOrEmpty(log.StockDescription))
				{
					description = log.StockDescription;
				}
				if (!string.IsNullOrEmpty(log.ColourDescription))
				{
					description += (description.Length > 0 ? " " : "") + log.ColourDescription;
				}
				sb.Append($@"
            <tr style='font-size: 12px;'>
                <td colspan='5' style='padding-left: 15px;'>{description}</td>
            </tr>");
			}

			var matchingRefs = refs.Where(r => r.LineNo == log.LineNo && log.StyleCode != "Reason").ToList();
			if (matchingRefs.Any())
			{
				foreach (var matchingRef in matchingRefs)
				{
					sb.Append($@"
                <tr style='font-size: 12px;'>
                    <td colspan='5'> - Reason: {matchingRef.TransReference}</td>
                </tr>");
				}
			}
		}
		if (refs.Any())
		{
			var unmatchedRefs = refs.Where(r => !receiptDto.logs.Any(l => l.LineNo == r.LineNo)).ToList();
			if (unmatchedRefs.Any())
			{
				outsideReasons.Append("<tr><td colspan='5'>Other Reasons:</td></tr>");
				foreach (var refItem in unmatchedRefs)
				{
					outsideReasons.Append($"<tr style='font-size: 12px;'><td colspan='5'> - Details : {refItem.TransReference}</td></tr>");
				}
			}
		}

		return sb.ToString() + outsideReasons.ToString();
	}

	/// <summary>
	/// Aggregates layby items by style/color/size to show net quantities.
	/// Items that have been completely removed (net quantity = 0) are excluded.
	/// This version works with domain Translog objects.
	/// </summary>
	private TranslogDto[] AggregateLaybyItems(IEnumerable<Translog> logs)
	{
		// Only process item-related logs that have valid style codes and positive selling prices
		var itemLogs = logs.Where(log =>
			!string.IsNullOrWhiteSpace(log.StyleCode) &&
			log.SellingPrice > 0 &&
			log.Quantity > 0 &&
			!IsLaybyCodeEntry(log.StyleCode))
			.ToList();

		// Group by style + color + size
		var itemGroups = itemLogs
			.GroupBy(log => new { log.StyleCode, log.ColourCode, log.SizeCode })
			.Select(group => new TranslogDto
			{
				StyleCode = group.Key.StyleCode,
				ColourCode = group.Key.ColourCode,
				SizeCode = group.Key.SizeCode,
				Quantity = group.Sum(log => log.Quantity ?? 0),
				SellingPrice = group.First().SellingPrice ?? 0,
				LineNo = (int)(group.First().LineNo),
				TransactionDate = group.First().TransactionDate,
				NettSelling = group.First().NettSelling
			})
			.Where(item => item.Quantity > 0) // Filter out items with zero or negative net quantity
			.ToArray();

		return itemGroups;
	}

	/// <summary>
	/// Aggregates layby items for reprints, including both positive and negative quantities.
	/// This properly handles removed items (transType 7) by netting them against added items.
	/// Items that have been completely removed (net quantity = 0) are excluded.
	/// </summary>
	private TranslogDto[] AggregateLaybyItemsForReprint(TranslogDto[] logs)
	{

		// Process ALL item-related logs including negative quantities (removed items)
		var itemLogs = logs.Where(log =>
			!string.IsNullOrWhiteSpace(log.StyleCode) &&
			log.SellingPrice > 0 && // Only positive selling prices (items, not payments)
			!IsLaybyCodeEntry(log) && // Exclude layby code entries
			!IsDateEntry(log.StyleCode)) // Exclude date entries like "04/07/25"
			.ToList();

		// Group by style + color + size and sum quantities (including negative ones)
		var itemGroups = itemLogs
			.GroupBy(log => new { log.StyleCode, log.ColourCode, log.SizeCode })
			.Select(group => {
				var netQuantity = group.Sum(log => log.Quantity);

				return new TranslogDto
				{
					StyleCode = group.Key.StyleCode,
					ColourCode = group.Key.ColourCode,
					SizeCode = group.Key.SizeCode,
					Quantity = netQuantity, // Sum includes negative quantities
					SellingPrice = group.First(log => log.Quantity > 0).SellingPrice, // Use price from positive entry
					LineNo = (int)(group.First().LineNo),
					TransactionDate = group.First().TransactionDate,
					NettSelling = group.First(log => log.Quantity > 0).NettSelling
				};
			})
			.Where(item => {
				bool include = item.Quantity > 0;
				return include;
			}) // Only include items with positive net quantity
			.ToArray();

		return itemGroups;
	}

	/// <summary>
	/// Aggregates layby items by style/color/size to show net quantities.
	/// Items that have been completely removed (net quantity = 0) are excluded.
	/// This version works with TranslogDto objects.
	/// </summary>
	private TranslogDto[] AggregateLaybyItems(TranslogDto[] logs)
	{
		// Only process item-related logs that have valid style codes and positive selling prices
		var itemLogs = logs.Where(log =>
			!string.IsNullOrWhiteSpace(log.StyleCode) &&
			log.SellingPrice > 0 &&
			log.Quantity > 0 &&
			!IsLaybyCodeEntry(log))
			.ToList();

		// Group by style + color + size
		var itemGroups = itemLogs
			.GroupBy(log => new { log.StyleCode, log.ColourCode, log.SizeCode })
			.Select(group => new TranslogDto
			{
				StyleCode = group.Key.StyleCode,
				ColourCode = group.Key.ColourCode,
				SizeCode = group.Key.SizeCode,
				Quantity = group.Sum(log => log.Quantity),
				SellingPrice = group.First().SellingPrice, // Use price from first occurrence
				LineNo = group.First().LineNo,
				TransactionDate = group.First().TransactionDate
			})
			.Where(item => item.Quantity > 0) // Filter out items with zero or negative net quantity
			.ToArray();

		return itemGroups;
	}

	private string GenerateLaybyItemRows(ReceiptTransactionDto receiptDto, TranslogDto[] logs)
	{
		var sb = new StringBuilder();
		var outsideReasons = new StringBuilder();

		var refs = receiptDto.refs ?? Enumerable.Empty<Transref>();

		// Use aggregated items instead of raw logs - use reprint aggregation to handle removed items
		var itemLogs = AggregateLaybyItemsForReprint(logs);

		foreach (var log in itemLogs)
		{
			var price = FinancialRound(log.SellingPrice);

			sb.Append($@"
        <tr>
            <td>{log.StyleCode}</td>
            <td>{log.ColourCode}</td>
            <td>{log.SizeCode}</td>
            <td>{log.Quantity}</td>
            <td class='text-right'>{price}</td>
        </tr>");

			// Add description row if either description exists
			if (!string.IsNullOrEmpty(log.StockDescription) || !string.IsNullOrEmpty(log.ColourDescription))
			{
				string description = "";
				if (!string.IsNullOrEmpty(log.StockDescription))
				{
					description = log.StockDescription;
				}
				if (!string.IsNullOrEmpty(log.ColourDescription))
				{
					description += (description.Length > 0 ? " " : "") + log.ColourDescription;
				}
				sb.Append($@"
            <tr style='font-size: 12px;'>
                <td colspan='5' style='padding-left: 15px;'>{description}</td>
            </tr>");
			}

			// Append any matching references
			var matchingRefs = refs.Where(r => r.LineNo == log.LineNo).ToList();
			foreach (var matchingRef in matchingRefs)
			{
				sb.Append($@"
            <tr style='font-size: 12px;'>
                <td colspan='5'> - Reason: {matchingRef.TransReference}</td>
            </tr>");
			}
		}

		// Append unmatched references if any
		var unmatchedRefs = refs.Where(r => !logs.Any(l => l.LineNo == r.LineNo)).ToList();
		if (unmatchedRefs.Any())
		{
			outsideReasons.Append("<tr><td colspan='5'>Other Reasons:</td></tr>");
			foreach (var refItem in unmatchedRefs)
			{
				outsideReasons.Append($"<tr style='font-size: 12px;'><td colspan='5'> - Details : {refItem.TransReference}</td></tr>");
			}
		}

		// Extract the layby code and display it clearly below the table
		var laybyCode = logs.FirstOrDefault(log => IsLaybyCodeEntry(log))?.StyleCode ?? "N/A";
		sb.Append($@"
    <div class='layby-details'>
        <p><strong>Layby Code:</strong> {laybyCode}</p>
    </div>");

		return sb.ToString() + outsideReasons.ToString();
	}

	/// <summary>
	/// Generates HTML rows for layby return/cancellation receipt items with layby code display.
	/// </summary>
	private string GenerateLaybyReturnItemRows(ReceiptTransactionDto receiptDto, TranslogDto[] logs, string laybyCode)
	{
		var sb = new StringBuilder();
		var outsideReasons = new StringBuilder();

		var refs = receiptDto.refs ?? Enumerable.Empty<Transref>();

		// For both cancellations (TransType 9) and returns (TransType 2), use aggregation to handle removed items
		TranslogDto[] itemLogs = AggregateLaybyItemsForReprint(logs);

		foreach (var log in itemLogs)
		{
			var price = FinancialRound(log.SellingPrice);

			sb.Append($@"
        <tr>
            <td>{log.StyleCode}</td>
            <td>{log.ColourCode}</td>
            <td>{log.SizeCode}</td>
            <td>{log.Quantity}</td>
            <td class='text-right'>{price}</td>
        </tr>");

			// Add description row if either description exists
			if (!string.IsNullOrEmpty(log.StockDescription) || !string.IsNullOrEmpty(log.ColourDescription))
			{
				string description = "";
				if (!string.IsNullOrEmpty(log.StockDescription))
				{
					description = log.StockDescription;
				}
				if (!string.IsNullOrEmpty(log.ColourDescription))
				{
					description += (description.Length > 0 ? " " : "") + log.ColourDescription;
				}
				sb.Append($@"
            <tr style='font-size: 12px;'>
                <td colspan='5' style='padding-left: 15px;'>{description}</td>
            </tr>");
			}

			// Append any matching references
			var matchingRefs = refs.Where(r => r.LineNo == log.LineNo).ToList();
			foreach (var matchingRef in matchingRefs)
			{
				sb.Append($@"
            <tr style='font-size: 12px;'>
                <td colspan='5'> - Reason: {matchingRef.TransReference}</td>
            </tr>");
			}
		}

		// Append unmatched references if any
		var unmatchedRefs = refs.Where(r => !logs.Any(l => l.LineNo == r.LineNo)).ToList();
		foreach (var refItem in unmatchedRefs)
		{
			outsideReasons.Append($"<tr style='font-size: 12px;'><td colspan='5'> - Details : {refItem.TransReference}</td></tr>");
		}

		// Add the layby code display
		sb.Append($@"
    <div class='layby-details' style='margin-top: 15px; padding: 10px 0; border-top: 1px solid #eee;'>
        <p><strong>Layby Code:</strong> {laybyCode}</p>
    </div>");

		return sb.ToString() + outsideReasons.ToString();
	}

	/// <summary>
	/// Determines if a log entry is a layby code entry (with zero quantity and positive price).
	/// </summary>
	private bool IsLaybyCodeEntry(TranslogDto log)
	{
		return log.Quantity == 0;
	}

	/// <summary>
	/// Determines if a style code is a date entry (format: dd/MM/yy).
	/// </summary>
	private bool IsDateEntry(string styleCode)
	{
		if (string.IsNullOrWhiteSpace(styleCode))
			return false;

		// Check if it matches date pattern like "04/07/25" or "17/07/25"
		return System.Text.RegularExpressions.Regex.IsMatch(styleCode, @"^\d{2}/\d{2}/\d{2}$");
	}

	/// <summary>
	/// Determines if a style code represents a layby code entry.
	/// This is a simplified version that checks for common layby code patterns.
	/// </summary>
	private bool IsLaybyCodeEntry(string styleCode)
	{
		// Layby codes typically start with "OL" or similar patterns
		// This is a simplified check - you may need to adjust based on your layby code format
		return string.IsNullOrWhiteSpace(styleCode) ||
			   styleCode.StartsWith("OL", StringComparison.OrdinalIgnoreCase) ||
			   styleCode.Length > 10; // Layby codes are typically longer than regular style codes
	}

	/// <summary>
	/// Determines if a layby transaction is actually an edit by checking if the most recent
	/// layby line has transType 5 (added) or 7 (removed).
	/// </summary>
	private bool IsLaybyEdit(ReceiptTransactionDto receiptDto)
	{
		if (receiptDto?.logs == null || !receiptDto.logs.Any())
		{
			return false;
		}

		// Get the most recent layby line (highest transaction date or last in sequence)
		var mostRecentLine = receiptDto.logs
			.Where(log => !string.IsNullOrWhiteSpace(log.StyleCode) &&
						  !IsLaybyCodeEntry(log.StyleCode) &&
						  !IsDateEntry(log.StyleCode))
			.OrderByDescending(log => log.TransactionDate ?? DateTime.MinValue)
			.ThenByDescending(log => log.LineNo)
			.FirstOrDefault();

		// If the most recent line has transType 5 (added) or 7 (removed), it's an edit
		return mostRecentLine?.TransType == 5 || mostRecentLine?.TransType == 7;
	}

	/// <summary>
	/// Determines if a layby is an edit by checking the layby lines for transType 5 (added) or 7 (removed).
	/// This version works with actual layby lines from the layby system.
	/// </summary>
	private bool IsLaybyEditFromLaybyLines(IEnumerable<Laybyline> laybyLines)
	{
		if (laybyLines == null || !laybyLines.Any())
		{
			return false;
		}

		// Get the last layby line and check if it has transType 5 (added) or 7 (removed)
		var lastLaybyLine = laybyLines.Last();
		return lastLaybyLine.TransType == 5 || lastLaybyLine.TransType == 7;
	}

	/// <summary>
	/// Generates complete HTML section for original layby items including table structure.
	/// </summary>
	private string GenerateOriginalLaybyItemsSection(Translog[] logs)
	{
		var originalItems = logs.Where(log =>
			!string.IsNullOrWhiteSpace(log.StyleCode) &&
			log.SellingPrice.HasValue && log.SellingPrice > 0 &&
			log.Quantity.HasValue && log.Quantity > 0 &&
			!IsLaybyCodeEntry(log.StyleCode) &&
			!IsDateEntry(log.StyleCode) &&
			log.TransType == 1).ToList(); // transType 1 = original items

		if (!originalItems.Any())
		{
			return ""; // Return empty string if no original items
		}

		var sb = new StringBuilder();
		sb.AppendLine("<div class=\"section\">");
		sb.AppendLine("    <div class=\"section-title\">Original Layby Items</div>");
		sb.AppendLine("    <table class=\"items-table\">");
		sb.AppendLine("        <tr>");
		sb.AppendLine("            <th>Style</th>");
		sb.AppendLine("            <th>Colour</th>");
		sb.AppendLine("            <th>Size</th>");
		sb.AppendLine("            <th class=\"qty-col\">Qty</th>");
		sb.AppendLine("            <th class=\"price-col\">Price</th>");
		sb.AppendLine("        </tr>");

		foreach (var item in originalItems)
		{
			sb.AppendLine("        <tr>");
			sb.AppendFormat("            <td>{0}</td>", item.StyleCode ?? "");
			sb.AppendFormat("            <td>{0}</td>", item.ColourCode ?? "");
			sb.AppendFormat("            <td>{0}</td>", item.SizeCode ?? "");
			sb.AppendFormat("            <td class=\"qty-col\">{0}</td>", item.Quantity);
			sb.AppendFormat("            <td class=\"price-col\">${0}</td>", item.SellingPrice.Value.ToString("F2"));
			sb.AppendLine("        </tr>");
		}

		sb.AppendLine("    </table>");
		sb.AppendLine("</div>");

		return sb.ToString();
	}

	/// <summary>
	/// Generates complete HTML section for added layby items including table structure.
	/// </summary>
	private string GenerateAddedLaybyItemsSection(Translog[] logs)
	{
		var addedItems = logs.Where(log =>
			!string.IsNullOrWhiteSpace(log.StyleCode) &&
			log.SellingPrice.HasValue && log.SellingPrice > 0 &&
			log.Quantity.HasValue && log.Quantity > 0 &&
			!IsLaybyCodeEntry(log.StyleCode) &&
			!IsDateEntry(log.StyleCode) &&
			log.TransType == 5).ToList(); // transType 5 = added items

		if (!addedItems.Any())
		{
			return ""; // Return empty string if no added items
		}

		var sb = new StringBuilder();
		sb.AppendLine("<div class=\"section\">");
		sb.AppendLine("    <div class=\"section-title\">Items Added</div>");
		sb.AppendLine("    <table class=\"items-table\">");
		sb.AppendLine("        <tr>");
		sb.AppendLine("            <th>Style</th>");
		sb.AppendLine("            <th>Colour</th>");
		sb.AppendLine("            <th>Size</th>");
		sb.AppendLine("            <th class=\"qty-col\">Qty</th>");
		sb.AppendLine("            <th class=\"price-col\">Price</th>");
		sb.AppendLine("        </tr>");

		foreach (var item in addedItems)
		{
			sb.AppendLine("        <tr>");
			sb.AppendFormat("            <td>{0}</td>", item.StyleCode ?? "");
			sb.AppendFormat("            <td>{0}</td>", item.ColourCode ?? "");
			sb.AppendFormat("            <td>{0}</td>", item.SizeCode ?? "");
			sb.AppendFormat("            <td class=\"qty-col\">{0}</td>", item.Quantity);
			sb.AppendFormat("            <td class=\"price-col\">${0}</td>", item.SellingPrice.Value.ToString("F2"));
			sb.AppendLine("        </tr>");
		}

		sb.AppendLine("    </table>");
		sb.AppendLine("</div>");

		return sb.ToString();
	}

	/// <summary>
	/// Generates complete HTML section for removed layby items including table structure.
	/// </summary>
	private string GenerateRemovedLaybyItemsSection(Translog[] logs)
	{
		var removedItems = logs.Where(log =>
			!string.IsNullOrWhiteSpace(log.StyleCode) &&
			log.SellingPrice.HasValue && log.SellingPrice > 0 &&
			log.Quantity.HasValue &&
			!IsLaybyCodeEntry(log.StyleCode) &&
			!IsDateEntry(log.StyleCode) &&
			log.TransType == 7).ToList(); // transType 7 = removed items

		if (!removedItems.Any())
		{
			return ""; // Return empty string if no removed items
		}

		var sb = new StringBuilder();
		sb.AppendLine("<div class=\"section\">");
		sb.AppendLine("    <div class=\"section-title\">Items Removed</div>");
		sb.AppendLine("    <table class=\"items-table\">");
		sb.AppendLine("        <tr>");
		sb.AppendLine("            <th>Style</th>");
		sb.AppendLine("            <th>Colour</th>");
		sb.AppendLine("            <th>Size</th>");
		sb.AppendLine("            <th class=\"qty-col\">Qty</th>");
		sb.AppendLine("            <th class=\"price-col\">Price</th>");
		sb.AppendLine("        </tr>");

		foreach (var item in removedItems)
		{
			sb.AppendLine("        <tr>");
			sb.AppendFormat("            <td>{0}</td>", item.StyleCode ?? "");
			sb.AppendFormat("            <td>{0}</td>", item.ColourCode ?? "");
			sb.AppendFormat("            <td>{0}</td>", item.SizeCode ?? "");
			sb.AppendFormat("            <td class=\"qty-col\">{0}</td>", Math.Abs(item.Quantity.Value)); // Show positive quantity for display
			sb.AppendFormat("            <td class=\"price-col\">${0}</td>", item.SellingPrice.Value.ToString("F2"));
			sb.AppendLine("        </tr>");
		}

		sb.AppendLine("    </table>");
		sb.AppendLine("</div>");

		return sb.ToString();
	}



	/// <summary>
	/// Generates total details section for layby edit receipts.
	/// </summary>
	private string GenerateLaybyEditTotalDetails(TranslogDto[] logs, TranspayDto[] pays, float amountDue)
	{
		var sb = new StringBuilder();

		// Calculate totals using aggregated items
		var aggregatedItems = AggregateLaybyItems(logs);
		float totalItemsCost = aggregatedItems.Sum(log => log.SellingPrice * log.Quantity);

		// Calculate total prior payments (negative selling prices in logs)
		float totalPriorPayments = Math.Abs(logs.Where(log => log.SellingPrice < 0).Sum(log => log.SellingPrice));

		// Calculate current payments
		float currentPayments = pays?.Sum(p => p.PayAmount) ?? 0;

		// Remaining balance = Total items cost - Prior payments - Current payments
		float remainingAmount = Math.Max(totalItemsCost - totalPriorPayments - currentPayments, 0);

		sb.AppendFormat("<p><strong>Total Items:</strong> {0}</p>", totalItemsCost.ToString("F2"));
		if (totalPriorPayments > 0)
		{
			sb.AppendFormat("<p><strong>Prior Payments:</strong> {0}</p>", totalPriorPayments.ToString("F2"));
		}
		if (currentPayments > 0)
		{
			sb.AppendFormat("<p><strong>Current Payment:</strong> {0}</p>", currentPayments.ToString("F2"));
		}
		sb.AppendFormat("<p><strong>Remaining Balance:</strong> {0}</p>", remainingAmount.ToString("F2"));

		return sb.ToString();
	}


	//TODO In layby payment trans/TransType 5 when printing receipt the balance remaining is not correct
	private string GetItemHeaders(int transType)
	{
		return transType switch
		{
			12 => "<th>Style</th><th>Colour</th><th>Size</th><th>Qty</th><th>Quote Price</th>",
			_ => "<th>Style</th><th>Colour</th><th>Size</th><th>Qty</th><th>Price</th>"
		};
	}

	// Updated totals to handle TransType 5 (Layby Payment)
	private string GenerateTotalDetails(ReceiptTransactionDto receiptDto, string accountName = null, string accountCode = null )
	{
		var sb = new StringBuilder();
		// Use appropriate aggregation method based on transaction type
		TranslogDto[] aggregatedItems;
		if (receiptDto.TransType == 5) // Layby Payment
		{
			// Convert Translog to TranslogDto for layby payment aggregation
			var translogDtos = (receiptDto.logs ?? Enumerable.Empty<Translog>())
				.Select(log => new TranslogDto
				{
					StyleCode = log.StyleCode,
					ColourCode = log.ColourCode,
					SizeCode = log.SizeCode,
					Quantity = log.Quantity.Value,
					SellingPrice = log.SellingPrice.Value,
					LineNo = (int)log.LineNo,
					TransactionDate = log.TransactionDate,
					TransactionTime = log.TransactionTime,
					NettSelling = log.NettSelling
				})
				.ToArray();

			// For layby payments, use the reprint aggregation method that handles removed items
			aggregatedItems = AggregateLaybyItemsForReprint(translogDtos);
		}
		else
		{
			// For other transaction types, use the standard aggregation
			aggregatedItems = AggregateLaybyItems(receiptDto.logs);
		}

		float total = aggregatedItems.Sum(log => log.SellingPrice * log.Quantity);

		switch (receiptDto.TransType)
		{
			case 1: // Sale
					// Display each payment type separately
				foreach (var payment in receiptDto.pays)
				{
					string paymentTypeName = payment.PaymentType ?? "Cash";
					string paymentAmount = FinancialRound(payment.PayAmount ?? 0);

					if (paymentTypeName == "Customer Account")
					{
						// If either accountName or accountCode is missing, just omit it
						string details = $"{paymentTypeName}";
						if (!string.IsNullOrWhiteSpace(accountName))
							details += $" {accountName}";
						if (!string.IsNullOrWhiteSpace(accountCode))
							details += $" ({accountCode})";

						sb.Append($"<div>{details}: {paymentAmount}</div>");
					}
					else
					{
						sb.Append($"<div>{paymentTypeName}: {paymentAmount}</div>");
					}
				}
				float paymentTotal = receiptDto.pays.Sum(pay => pay.PayAmount ?? 0);
				float difference = paymentTotal - total;
				if (difference < 0)
				{
					sb.Append($"<div>Deposit: {FinancialRound(-difference)}</div>");
				}
				sb.Append($"<div>Sale Total: {FinancialRound(total)}</div>");
				if (difference > 0)
				{
					sb.Append($"<div>CHANGE: {FinancialRound(difference)}</div>");
				}
				sb.Append($"<div>GST: {FinancialRound(total / 11)}</div>");
				break;

			case 4: // Layby
				float laybyDeposit = receiptDto.pays.Sum(pay => pay.PayAmount ?? 0);
				sb.Append($"<div>Layby Total: {FinancialRound(total)}</div>");

				// Display each payment type separately
				foreach (var payment in receiptDto.pays)
				{
					string paymentTypeName = payment.PaymentType ?? "Cash";
					sb.Append($"<div>{paymentTypeName} Paid: {FinancialRound(payment.PayAmount ?? 0)}</div>");
				}
				// If the sum of payments is less than the total, fill the difference with a deposit
				float laybyDifference = laybyDeposit - total;
				if (laybyDifference > 0)
				{
					sb.Append($"<div>CHANGE: {FinancialRound(laybyDifference)}</div>");
				}
				sb.Append($"<div>Balance Due: {FinancialRound(total - laybyDeposit)}</div>");
				sb.Append($"<div>GST: {FinancialRound(total / 11)}</div>");
				break;

			case 5: // Layby Payment
				// Show total items cost first
				sb.Append($"<div>Total: {FinancialRound(total)}</div>");

				// Display each payment type separately
				foreach (var payment in receiptDto.pays)
				{
					string paymentTypeName = payment.PaymentType ?? "Cash";
					sb.Append($"<div>{paymentTypeName} Payment: {FinancialRound(payment.PayAmount ?? 0)}</div>");
				}
				break;

			case 11: // Customer Order
				float orderDeposit = receiptDto.pays.Sum(pay => pay.PayAmount ?? 0);
				sb.Append($"<div>Order Total: {FinancialRound(total)}</div>");

				// Display each payment type separately
				foreach (var payment in receiptDto.pays)
				{
					string paymentTypeName = payment.PaymentType ?? "Cash";
					sb.Append($"<div>{paymentTypeName} Paid: {FinancialRound(payment.PayAmount ?? 0)}</div>");
				}
				float orderDifference = orderDeposit - total;
				if (orderDifference > 0)
				{
					sb.Append($"<div>CHANGE: {FinancialRound(orderDifference)}</div>");
				}
				sb.Append($"<div>Balance Due: {FinancialRound(total - orderDeposit)}</div>");
				sb.Append($"<div>GST: {FinancialRound(total / 11)}</div>");
				break;

			case 2:
				sb.Append($"<div class='negative'>Return Total: {FinancialRound(total)}</div>");
				sb.Append($"<div>Refund Amount: {FinancialRound(total)}</div>");
				sb.Append($"<div class='negative'>GST: {FinancialRound(total / 11)}</div>");
				break;

			case 3:
				float exchangePaymentTotal = receiptDto.pays.Sum(pay => pay.PayAmount ?? 0);
				float exchangeAmount = Math.Abs(exchangePaymentTotal);
				sb.Append(exchangePaymentTotal < 0
					? $"<div>Refund Total: {FinancialRound(exchangeAmount)}</div>"
					: $"<div>Sale Total: {FinancialRound(exchangeAmount)}</div>");
				sb.Append($"<div>GST: {FinancialRound(exchangeAmount / 11)}</div>");
				break;

			case 12:
				sb.Append($"<div>Quote Total: {FinancialRound(total)}</div>");
				sb.Append($"<div>GST Included: {FinancialRound(total / 11)}</div>");
				break;
		}

		return sb.ToString();
	}

	private string GetQuoteDisclaimer(int transType)
	{
		return transType == 12
			? "<div class='disclaimer'>* Quoted prices are valid for 14 days</div>"
			: "";
	}

	private string GetTransactionTypeHeader(int transType)
	{
		return transType switch
		{
			1 => "SALE RECEIPT",
			2 => "RETURN RECEIPT",
			3 => "EXCHANGE RECEIPT",
			4 => "LAYBY RECEIPT",
			5 => "LAYBY PAYMENT RECEIPT",
			11 => "CUSTOMER ORDER",
			12 => "QUOTE",
			_ => "RECEIPT"
		};
	}

	private string FinancialRound(float value)
	{
		decimal decimalValue = (decimal)value;
		decimal rounded = Math.Round(decimalValue, 2, MidpointRounding.ToEven);
		return rounded.ToString("F2");
	}

	private byte[] GenerateGiftVoucherPdf(string organizationName, string voucherNumber, decimal voucherValue, string voucherType)
	{
		string htmlContent = BuildGiftVoucherHtml(organizationName, voucherNumber, voucherValue, voucherType);

		var converter = new HtmlToPdf();
		converter.Options.MarginLeft = 10;
		converter.Options.MarginRight = 10;
		converter.Options.MarginTop = 10;
		converter.Options.MarginBottom = 10;
		converter.Options.PdfPageSize = PdfPageSize.A4;
		converter.Options.PdfPageOrientation = PdfPageOrientation.Portrait;

		var pdfDocument = converter.ConvertHtmlString(htmlContent);
		using (var memoryStream = new MemoryStream())
		{
			pdfDocument.Save(memoryStream);
			pdfDocument.Close();
			return memoryStream.ToArray();
		}
	}

	private string BuildGiftVoucherHtml(
		string organizationName,
		string voucherNumber,
		decimal voucherValue,
		string voucherType)
	{
		string baseDirectory = AppContext.BaseDirectory;
		string templatePath = Path.Combine(baseDirectory, "Email", "GiftVoucherTemplate.html");
		string template = System.IO.File.ReadAllText(templatePath);

		// Decide title & description based on type
		string voucherTitle;
		string voucherDescription;

		if (voucherType == "Credit Note")
		{
			voucherTitle = "Credit Note";
			voucherDescription =
				"Thank you for your purchase. This credit note entitles the bearer to goods or services " +
				"to the value shown above. Please present this voucher at the time of purchase to redeem.";
		}
		else if (voucherType == "Gift Card")
		{
			voucherTitle = "Gift Card";
			voucherDescription =
				"Thank you for purchasing a gift card. Use this card at checkout to redeem your balance. " +
				"Treat it like cash—no change will be given on remaining balance.";
		}
		else
		{
			voucherTitle = "Voucher";
			voucherDescription =
				"This voucher entitles the bearer to goods or services to the value shown above.";
		}

		// Generate barcode
		string barcodeImageBase64 = GenerateBarcodeImage(voucherNumber);
		string barcodeDataUrl = $"data:image/png;base64,{barcodeImageBase64}";

		// Do all replacements
		string html = template
			.Replace("{{ORGANISATION_NAME}}", organizationName)
			.Replace("{{VOUCHER_TITLE}}", voucherTitle)
			.Replace("{{VOUCHER_VALUE}}", voucherValue.ToString("0.00"))
			.Replace("{{VOUCHER_NUMBER}}", voucherNumber)
			.Replace("{{BARCODE_URL}}", barcodeDataUrl)
			.Replace("{{ISSUE_DATE}}", _storeTimeContext.StoreLocalTime.ToString("dd/MM/yyyy"))
			.Replace("{{VOUCHER_DESCRIPTION}}", voucherDescription);

		return html;
	}

	private string GenerateBarcodeImage(string barcodeData)
	{
		try
		{
			// Create barcode using SkiaSharp-based BarcodeStandard
			var barcode = new Barcode();
			// Configure barcode settings using SkiaSharp colors and fonts
			barcode.Encode(BarcodeStandard.Type.Code128, barcodeData,
				new SKColorF(0, 0, 0), // Black foreground
				new SKColorF(1, 1, 1), // White background
				300, 100);
			barcode.AlternateLabel = barcodeData; // Show the code beneath the barcode
			barcode.IncludeLabel = true;
			// Use MemoryStream to save the barcode image
			using (var memoryStream = new MemoryStream())
			{
				// Save the barcode to the memory stream
				barcode.SaveImage(memoryStream, SaveTypes.Png); // Specify SaveTypes.Png as the format
																// Reset position to beginning of stream
				memoryStream.Position = 0;
				// Convert to byte array
				byte[] barcodeBytes = memoryStream.ToArray();
				// Convert to base64 string
				return Convert.ToBase64String(barcodeBytes);
			}
		}
		catch (Exception ex)
		{
			// Log the exception if possible
			Console.WriteLine($"Barcode generation error: {ex.Message}");
			// If barcode generation fails, return empty string
			return "";
		}
	}

	public class GiftVoucherRequest
	{
		public string Email { get; set; }
		public string VoucherNumber { get; set; }
		public decimal VoucherValue { get; set; }
		public string VoucherType { get; set; }
	}

	public class TransNoRequest
	{
		public string Email { get; set; }
		public int? TransNo { get; set; }
	}

	public class EmailRequest
	{
		public string Email { get; set; }
		public ReceiptTransactionDto ReceiptDto { get; set; }
		public string clientName { get; set; } = "";
		public string clientCode { get; set; } = "";
		public string customerPoints { get; set; } = "";
		public string pointsEarned { get; set; } = "";
		public string orderCode { get; set; } = "";
		public string accountCode { get; set; } = "";
		public string accountName { get; set; } = "";
	}

	public class LaybyPaymentEmailRequest
	{
		public string Email { get; set; }
		public ReceiptTransactionDto ReceiptDto { get; set; }
		public string receiptType { get; set; }
		public TranslogDto[] logs { get; set; }
		public TranspayDto[] pays { get; set; }
		public int transNo { get; set; }
		public float amountDue { get; set; }
		public float change { get; set; }
	}
}
