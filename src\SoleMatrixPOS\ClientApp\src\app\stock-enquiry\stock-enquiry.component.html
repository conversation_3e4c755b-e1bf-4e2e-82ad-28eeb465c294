<nav class="navbar navbar-light navbar-expand-lg bg-light">
	<div class="container-fluid">
		<div class="navbar-brand" (click)="goHome()">
			<img src="assets/logo.svg" alt="Solematrix Logo" />
		</div>
		<button class="navbar-toggler" type="button" (click)="collapsed = !collapsed"
			aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
			<span class="navbar-toggler-icon"></span>
		</button>

		<div class="pt-3 pl-5">
			<h2>{{_pageName}}</h2>
		</div>

		<div class="navbar-collapse collapsed" [class.collapse]="collapsed" id="navbarSupportedContent">
			<ul class="navbar-nav ml-auto">
				<!-- <li class="nav-item" *ngIf="storeInfo$ | async as store">
					<span class="navbar-text" *ngIf="staff$ | async as staff">{{staff.name}}</span>
					<span class="navbar-text">Store ID: <b>{{store.storeId}}</b></span>
					<span class="navbar-text">Store Name: <b>{{store.storeName}}</b> </span>
					<span class="navbar-text">{{date}}</span>
					<span class="navbar-text">{{rxTime | date: 'hh:mm:ss a'}}</span>
				</li> -->
				<li class="nav-item" *ngIf="staff$ | async as staff">
					<span class="nav-link text-dark">Logged in as: {{ staff.name }}</span>
				</li>
				
				<!-- <li class="nav-item active" *ngIf="!currentRoute.startsWith('/support')">
					<a class="nav-link" href="#">Support <span class="sr-only">(current)</span></a>
				</li> -->

			</ul>
		</div>
	</div>
</nav>

<!-- Main Container -->
<div class="container-fluid border-wrapper">
    <div class="row justify-content-center search-bar">
        <pos-item-nosize-lookup (result)="stockItemLookup($event)"></pos-item-nosize-lookup>
    </div>

    <!-- First Main Header with dynamic color dropdown -->
    <div class="row mb-1 sticky-header">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th scope="col">Style Code</th>
                        <th scope="col">Style Description</th>
                        <th scope="col">Colour Code</th>
                        <th scope="col">Maker Code</th>
                        <th scope="col">Label</th>
                        <th scope="col">Department Code</th>
                        <th scope="col">Retail Price</th>
                        <th scope="col">Colour</th>
                    </tr>
                </thead>
                <tbody *ngIf="header$ | async; let item">
                    <tr>
                        <td>{{ item.styleCode }}</td>
                        <td><b>{{ item.styleDescription }}</b></td>
                        <td><b>{{ item.colorCode }}</b></td>
                        <td>{{ item.makerCode }}</td>
                        <td>{{ item.lableCode }}</td>
                        <td>{{ item.departmentCode }}</td>
                        <td>{{ item.retailPrice }}</td>
                        <td class="align-middle">
                            <div class="d-flex">
                                <pos-select-by-colour (selectedItemChanged)="selectedByColor($event)"></pos-select-by-colour>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Other content -->
    <div class="row mt-1 main-tables">
        <div class="col-md-9 table-responsive p-0">
            <pos-main-table></pos-main-table>
        </div>
        <div class="col-md-3 table-responsive">
            <pos-total-amount></pos-total-amount>
        </div>
    </div>
</div>
