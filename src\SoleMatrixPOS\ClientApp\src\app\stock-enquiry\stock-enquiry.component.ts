import { 
  StockEnquiryGetAllColorDto, 
  StockEnquiryHeaderDto, 
  StockItemDto 
} from '../pos-server.generated';
import * as StockEnquiryAction from '../reducers/stock-enquiry/stock-enquiry.actions';
import * as StockEnquireSelector from '../reducers/stock-enquiry/stock-enquiry.selectors';
import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import * as StoreSelector from '../reducers/store-info/store-info.selectors';
import * as StaffSelector from '../reducers/staff/staff.selectors';
import { AppState } from 'src/app/reducers';
import { Store } from '@ngrx/store';
import { Observable, Subscription, timer } from 'rxjs';
import { StaffLoginDto, StoreDetailsDto } from 'src/app/pos-server.generated';
import { map, share } from 'rxjs/operators';

@Component({
  selector: 'pos-stock-enquiry',
  templateUrl: './stock-enquiry.component.html',
  styleUrls: ['./stock-enquiry.component.scss']
})
export class StockEnquiryComponent implements OnInit, OnDestroy {

  header$: Observable<StockEnquiryHeaderDto>;
  colors$: Observable<StockEnquiryGetAllColorDto[]>;
  selectedItemChanged: string;

  colour: string;
  currentStyleCode: string;
  currentColorCode: string;

  collapsed: boolean;
  storeInfo$: Observable<StoreDetailsDto>;
  staff$: Observable<StaffLoginDto>;
  time = new Date();
  rxTime = new Date();
  intervalId;
  subscription: Subscription;
  _pageName = 'STOCK ENQUIRY';

  @Input()
  set pageName(pageName: string){
    this._pageName = (pageName && pageName.trim()) || "<no name set>";
  }

  constructor(private router: Router, private store: Store<AppState>) { }

  ngOnInit() {
    this.store.dispatch(StockEnquiryAction.init());
    this.header$ = this.store.select(StockEnquireSelector.stockEnquiryHeader);
    this.colors$ = this.store.select(StockEnquireSelector.stockEnquiryColor);

    this.storeInfo$ = this.store.select(StoreSelector.storeInfo);

    this.staff$ = this.store.select(StaffSelector.selectStaffLoginDto);

    this.subscription = timer(0, 1000)
    .pipe(
      map(() => new Date()),
      share()
    )
    .subscribe(time => {
      this.rxTime = time;
    });
  }

  ngOnDestroy(): void {
    clearInterval(this.intervalId);
    if(this.subscription){
      this.subscription.unsubscribe();
    }
  }


  goHome() {
		this.router.navigateByUrl('/home');
  }

  backBtnClick() {
    this.router.navigateByUrl('/home');
  }

  nextBtnClick() {
    this.router.navigateByUrl('/home');
  }

  stockItemLookup(item: StockItemDto) {
    this.store.dispatch(StockEnquiryAction.enquiryQuery({ stockItem: item }));
  }

  selectedByColor(item: StockEnquiryGetAllColorDto) {
    console.log(item);
    this.store.dispatch(StockEnquiryAction.enquiryColor({ itemStock: item }));
  }
}
